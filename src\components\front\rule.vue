<template>
  <div class="container sales-container">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="form"
      :showNum="4"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="init"
      :loadingState="exportLoading"
      :searchLoadingState="searchLoading"
    ></tg-search>
    <chartTable1
      :type="'rule'"
      :tableData="table_data"
      :chartData="chart_data"
      :searchDate="form.date_range"
    ></chartTable1>
  </div>
</template>
<script>
import chartTable1 from "./chartTable1.vue";
import { downLoadFile } from "@/public/downLoadFile";
import frontApi from "@/api/front";
import quickTime from "@/public/quickTime";

export default {
  data() {
    return {
      searchLoading: false,
      list: [],
      total: 0,
      detail_visible: false,
      page_size: 10,
      page: 1,
      form: { date_range: [], valid_status: undefined },
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "valid_status",
          label: "客户有效性",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "待定", id: "pending" },
            { name: "有效", id: "valid" },
            { name: "无效", id: "invalid" }
          ]
        }
      ],
      table_data: [],
      chart_data: {},
      loading: true,
      isExport: true,
      exportLoading: false,
      firstCome: true
    };
  },
  created() {
    if (this.$_has({ m: "front", o: "intention_export" })) {
      this.isExport = true;
    }
  },
  mounted() {},
  components: { chartTable1 },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId.toString();
    }
  },
  watch: {
    schoolIds: {
      handler(val) {
        if (this.firstCome) {
          this.setSearchDefault();
        }
        this.init();
      },
      immediate: true
    },
    "form.date_range": {
      handler(val) {
        this.firstCome = false;
      },
      deep: true
    }
  },
  methods: {
    init() {
      this.searchLoading = true;
      this.table_data = [];
      this.chart_data = {};
      const query = this.getSearch();
      frontApi.getCustomerIntention(query).then((res) => {
        if (+res.status === 200 && res.data) {
          this.searchLoading = false;
          this.table_data = res.data.data.results;
          this.chart_data = {
            total: res.data.data.customer_sum,
            data: res.data.data.results.map((item) => {
              return { name: item.intention_id, value: item.customer_number };
            })
          };
        }
      });
    },
    reset() {
      this.page = 1;
      this.setSearchDefault();
      this.init();
    },
    getSearch() {
      //   const { page, page_size } = this;
      const query = {
        // page,
        // page_size,
        valid_status: this.form.valid_status,
        department_id: this.schoolIds.split(",")
      };
      if (this.form.date_range && this.form.date_range.length) {
        const [start, end] = this.form.date_range;
        query.search_begin = start || "";
        query.search_end = end || "";
      }
      return query;
    },
    exportExcel() {
      if (!this.table_data) {
        this.$message.warning("暂无可导出数据!");
      }
      this.exportLoading = true;
      const query = this.getSearch();
      frontApi.exportCustomerIntention(query).then((res) => {
        downLoadFile(res, `意向级别列表`);
        this.exportLoading = false;
      });
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.form, "date_range", pastThirty);
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  margin: 0 !important;
  padding-top: 10px;
  box-sizing: border-box;
  .rate-list-container {
    width: 100%;
    height: 182px;
    padding: 32px 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 4px;
    > div {
      width: 186px;
      height: 100%;
      background: #f5f8fc;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      border-radius: 4px;
      padding: 16px 20px;
      box-sizing: border-box;
      font-weight: 400;

      .label-container {
        display: flex;
        align-items: center;
        img {
          width: 36px;
          height: 36px;
          margin-right: 10px;
        }
        span {
          font-size: 18px;
          color: #1f2d3d;
        }
      }
      .rate-content {
        font-size: 32px;
        line-height: 46px;
        color: #475669;
      }
    }
  }
}
.sales-container {
  padding-bottom: 16px;
}
</style>
