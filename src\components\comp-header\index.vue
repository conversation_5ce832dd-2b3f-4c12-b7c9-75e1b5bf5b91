<template>
  <div class="top_wrap">
    <div class="left_feature">
      <div class="course_status">
        <span
          class="dian"
          :style="{
            backgroundColor:
              liveStage !== 'not_started' && liveStage !== 'ongoing'
                ? 'red'
                : ''
          }"
        ></span
        ><span>{{
          liveStage === "not_started"
            ? "将要上课"
            : liveStage === "ongoing"
            ? "直播中"
            : "超时"
        }}</span>
        <span
          class="countdown"
          v-if="liveStage === 'not_started' || liveStage === 'yeondo'"
        >
          {{ s_to_hs(diffTime) }}
        </span>
      </div>
      <div class="live_title">{{ roomName }}</div>
      <div class="course_time" v-if="liveStage !== 'not_started'">
        课程时长：{{ course_len }}
      </div>
      <div class="live_time" v-if="liveStage === 'ongoing'">
        {{ s_to_hs(diffTime) }}
      </div>
    </div>
    <div
      class="right_feature"
      :style="{
        width: !teacherEnter ? '24px' : ''
      }"
    >
      <div v-if="teacherEnter && liveAudit !== 'Audit'" class="teacher_enter">
        <el-tooltip
          class="item"
          effect="dark"
          content="教师麦克风"
          placement="bottom"
        >
          <img
            :src="
              isAudioMuted
                ? require('@/assets/living/老师话筒.png')
                : require('@/assets/living/老师话筒禁用.png')
            "
            alt=""
            class="voice"
            v-throttle="toggleMuteAudio"
          />
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="教师视频"
          placement="bottom"
        >
          <img
            :src="
              isVideoMuted
                ? require('@/assets/living/摄像头.png')
                : require('@/assets/living/视频禁用.png')
            "
            alt=""
            class="head-camera"
            v-throttle="toggleMuteVideo"
          />
        </el-tooltip>
        <div class="line"></div>
        <el-tooltip
          class="item"
          effect="dark"
          content="录制"
          placement="bottom"
        >
          <img
            :src="
              recording
                ? require('@/assets/living/录制中.png')
                : require('@/assets/living/录课.png')
            "
            alt=""
            class="record"
            @click="startVideo"
          />
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="设置"
          placement="bottom"
        >
          <div class="setting" @click="$emit('handleShowDeviceDialog')"></div>
        </el-tooltip>
        <div class="line"></div>
      </div>
      <div
        class="back"
        v-if="liveAudit === 'Audit'"
        @click="$emit('leave_audit_out')"
      ></div>
      <el-dropdown v-else v-show="teacherEnter">
        <div class="back"></div>
        <el-dropdown-menu slot="dropdown" :append-to-body="false">
          <el-dropdown-item @click.native="$emit('leave_room')"
            >仅自己离开</el-dropdown-item
          >
          <el-dropdown-item @click.native="classOver"
            >全员下课</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
// import moment from "moment";
import rtc from "../mixins/rtc.js";
// import livePage from "@/api/livePage";
import config from "@/config";
import livePage from "@/api/livePage";

export default {
  mixins: [rtc],
  data() {
    return {
      roomName: "",
      diffTime: "",
      timer: "",
      course_len: ""
    };
  },
  props: {
    interval_end: {},
    interval_start: {},
    interval_starting: {},
    liveAudit: {
      type: String,
      default: ""
    }
  },
  computed: {
    liveStage() {
      return this.$store.getters.doneLiveStage;
    },
    isAudioMuted() {
      return this.$store.getters.doneAudioState;
    },
    isVideoMuted() {
      return this.$store.getters.doneVideoState;
    },
    recording() {
      return this.$store.getters.doneRecording;
    },
    shareScreen() {
      return this.$store.getters.doneShareScreen;
    },
    teacherEnter() {
      return this.$store.getters.doneTeacherEnter;
    }
  },
  components: {},
  mounted() {
    console.log("recording", this.recording);
    this.roomName = config.roomName;
    this.course_len = localStorage.getItem("live_courseLen");
  },
  watch: {
    liveStage: {
      handler: function (val) {
        if (this.liveStage === "not_started") {
          this.diffTime = this.interval_start;
          this.reGetCountdown();
        } else if (this.liveStage === "ongoing") {
          this.diffTime = this.interval_starting;
          this.reGetCountUp();
        } else {
          this.diffTime = this.interval_end;
          this.reGetCountUp();
        }
      },
      immediate: true,
      deep: true
    },
    diffTime: {
      handler: function (val) {
        const total =
          this.course_len.substring(0, this.course_len.lastIndexOf("时")) *
            3600 +
          this.course_len.substring(
            this.course_len.lastIndexOf("时") + 1,
            this.course_len.lastIndexOf("分")
          ) *
            60;

        if (val === 0 && this.liveStage === "not_started") {
          clearInterval(this.timer);
          this.timer = null;
          this.$store.dispatch("UPDATE_LIVE_STAGE", "ongoing");
        } else if (
          this.liveStage === "ongoing" &&
          this.interval_end === 0 &&
          val === total
        ) {
          clearInterval(this.timer);
          this.timer = null;
          this.$store.dispatch("UPDATE_LIVE_STAGE", "yeondo");
        }
      },
      deep: true
    }
  },

  beforeCreate() {},
  methods: {
    toggleMuteAudio() {
      this.$store.dispatch("UPDATE_AUDIO_STATE", !this.isAudioMuted);
    },
    toggleMuteVideo() {
      this.$store.dispatch("UPDATE_VIDEO_STATE", !this.isVideoMuted);
    },
    s_to_hs: function (value) {
      let secondTime = parseInt(value); // 秒
      let minuteTime = 0; // 分
      let hourTime = 0; // 时
      if (secondTime > 60) {
        // 如果秒数大于60，将秒数转换成整数
        // 获取分钟，除以60取整，得到整数分钟
        minuteTime = parseInt(secondTime / 60);
        // 获取秒数，秒数取余，得到整数秒数
        secondTime = parseInt(secondTime % 60);
        // 如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          // 获取小时，获取分钟除以60，得到整数小时
          hourTime = parseInt(minuteTime / 60);
          // 获取小时后取余的分，获取分钟除以60取余的分
          minuteTime = parseInt(minuteTime % 60);
        }
      }
      // 若秒数是个位数，前面用0补齐
      secondTime = secondTime < 10 ? "0" + secondTime : secondTime;
      let result = "" + secondTime + "";
      if (minuteTime > 0) {
        // 若分钟数是个位数，前面用0补齐
        minuteTime = minuteTime < 10 ? "0" + minuteTime : minuteTime;
        result = "" + minuteTime + ":" + result;
      } else {
        // 若分钟数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }

      if (hourTime > 0) {
        // 若小时数是个位数，前面用0补齐
        hourTime = hourTime < 10 ? "0" + hourTime : hourTime;
        result = "" + hourTime + ":" + result;
      } else {
        // 若小时数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }
      // console.log("result", result);
      return result;
    },
    reGetCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          if (this.diffTime > 0) {
            this.diffTime--;
          } else {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    },
    reGetCountUp() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          this.diffTime++;
        }, 1000);
      }
    },
    close() {
      console.log("123");
    },
    startVideo() {
      // if (!this.recording) {
      const h = this.$createElement;
      this.$confirm(
        h("div", [
          h(
            "p",
            "录制画面范围为当前尺寸教室窗口，录制声音为软件声音，当前录制课程画面质量为：高清（1280*720），为了保证录课质量，请检查以下注意事项："
          ),
          h("p", " 1.CPU要求：Intel i5 及以上；"),
          h("p", "2.占用空间：录制文件每分钟增加2MB。")
        ]),
        "提示",
        {
          customClass: "living_video",
          confirmButtonText: "开始录课",
          cancelButtonText: "否",
          type: "warning"
        }
      ).then(() => {
        this.$store.dispatch("UPDATE_RECORD_STATE", true);
        // this.$emit("startShareScreen", true);
        this.startRecordApi();
      });
      // }
    },
    startRecordApi() {
      const appDemo = document.getElementById("app");
      const width = appDemo.clientWidth;
      const height = appDemo.clientHeight;
      livePage
        .liveCreateCloudRecording({
          url: window.location.href,
          room_id: parseInt(config.roomId),
          width,
          height
        })
        .then((res) => {
          if (res.status && res.data.code === 0) {
            // this.$store.dispatch("UPDATE_RECORD_STATE", true);
          } else if (res.data.code === 1) {
            this.$message.info(res.data.message);
            this.$store.dispatch("UPDATE_RECORD_STATE", false);
          }
        });
    },
    classOver() {
      localStorage.removeItem("live_question_bank_id1");
      localStorage.removeItem("live_question_bank_id2");
      localStorage.removeItem("live_question_bank_level1");
      localStorage.removeItem("live_question_bank_level2");
      localStorage.removeItem("video_wall_student_id");
      localStorage.removeItem("video_wall_student_name");
      this.$emit("leave_over");
    }
    // startScreenShare() {
    //   if (!this.shareScreen) {
    //     this.$store.dispatch("UPDATE_SHARESCREEN_STATE", !this.shareScreen);
    //     this.$emit("startShareScreen");
    //   }
    // }
  }
};
</script>
<style lang="less" scoped>
.top_wrap {
  width: 100vw;
  height: 40px;
  background-color: #438eff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 12;
  .left_feature {
    width: 70%;
    height: 100%;
    display: flex;
    align-items: center;
    .course_status {
      // width: 128px;
      padding: 0 20px;
      height: 60%;
      text-align: center;
      border-radius: 40px;
      background: rgba(140, 186, 255, 0.5);
      color: #fff;
      font-size: 1.7vh;
      font-style: normal;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 39px;
      .dian {
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: #34ff41;
        border-radius: 50%;
        margin-right: 10px;
      }
      .countdown {
        margin-left: 10px;
      }
    }
    .live_title {
      color: #fff;
      font-size: 1.7vh;
      font-style: normal;
      font-weight: 700;
      margin-left: 30px;
    }
    .course_time {
      color: #fff;
      font-size: 1.7vh;
      font-style: normal;
      font-weight: 700;
      margin-left: 30px;
    }
    .live_time {
      color: #34ff41;
      font-size: 1.7vh;
      font-style: normal;
      font-weight: 700;
      margin-left: 30px;
    }
    .rewards_num {
      color: #fff;
      font-size: 1.7vh;
      font-style: normal;
      font-weight: 700;
      margin-left: 51px;
    }
  }
  .right_feature {
    width: 250px;
    height: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: flex-end;
    margin-right: 38px;
    .teacher_enter {
      width: 300px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .voice {
      // width: 28px;
      height: 28px;
      // background: url("../../../../assets/living/麦克风.png") no-repeat;
      // background-size: cover;
      cursor: pointer;
    }
    .head-camera {
      // width: 28px;
      height: 28px;
      // background: url("../../../../assets/living/摄像头.png") no-repeat;
      // background-size: cover;
      cursor: pointer;
    }
    .record {
      // width: 24px;
      height: 24px;
      // background: url("../../assets/living/录课.png") no-repeat;
      // background-size: contain;
      cursor: pointer;
    }
    .setting {
      width: 24px;
      height: 24px;
      background: url("../../assets/living/设置.png") no-repeat;
      background-size: contain;
      cursor: pointer;
    }
    .line {
      width: 2px;
      height: 20px;
      background-color: #63a1ff;
    }
    .back {
      width: 24px;
      height: 24px;
      background: url("../../assets/living/关闭.png") no-repeat;
      background-size: contain;
      cursor: pointer;
    }
  }
}
::v-deep .el-dropdown {
  margin-left: 15px;
}
::v-deep .el-dropdown > span {
  display: flex;
  flex-direction: row;
  align-items: center;
}
::v-deep .el-dropdown-menu {
  margin-top: 0;
  width: 164px;
  top: 30px !important;
  border: 1px solid #438eff !important;
  padding: 14px;
  right: 0px !important;
  box-sizing: border-box;
  z-index: 99;
}
::v-deep .popper__arrow {
  display: none;
}
::v-deep .el-dropdown-menu__item {
  line-height: 42px;
  border-radius: 4px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-dropdown-menu__item:nth-child(1) {
  background-color: #438eff;
  color: #fff;
  cursor: pointer;
}
.el-dropdown-menu__item:nth-child(2) {
  background-color: red;
  color: #fff;
  margin-top: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}
.el-dropdown-menu__item:nth-child(3) {
  color: #7c7c7c;
}

::v-deep .el-message-box {
  height: 400px !important;
}
.el-message-box__content {
  height: 300px;
}
</style>
