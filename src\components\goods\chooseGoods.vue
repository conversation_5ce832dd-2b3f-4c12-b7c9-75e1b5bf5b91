<template>
  <div class="choose-warehouse-goods">
    <el-input
      placeholder="请选择关联物品"
      readonly
      class="tg-select tg-select--dialog"
      @mouseenter.native="goods_flag = true"
      @mouseleave.native="goods_flag = false"
      show-word-limit
      :validate-event="false"
      @click.native="openGoodsDialog"
      v-model="goodsName"
    >
      <img
        slot="suffix"
        :src="
          !goods_flag
            ? require('../../assets/图片/icon_more.png')
            : require('../../assets/图片/icon_more_ac.png')
        "
        alt
        class="btn__img--dotted"
      />
    </el-input>
    <el-dialog
      :visible="goods_visible"
      title="选择物品"
      width="1016px"
      :before-close="handleClose"
      class="choose-match"
      :modal="has_modal"
      :append-to-body="true"
    >
      <div class="tg-dialog__content">
        <div class="class-list">
          <tg-search
            :searchTitle.sync="searchTitle"
            :form.sync="search"
            @reset="reset"
            @search="searchVal"
            :showNum="4"
          ></tg-search>
          <div class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              :data="tableData"
              tooltip-effect="dark"
              ref="table"
              :height="400"
              class="tg-table customer-table"
              :row-class-name="rowClassName"
              @selection-change="handleSelectionChange"
              :row-key="getRowKeys"
              @current-change="handleCurrentChange"
              @row-click="rowClick"
              :highlight-current-row="type == 'radio' ? true : false"
              border
            >
              <el-table-column
                type="selection"
                width="50"
                v-if="type != 'radio'"
                label-class-name="cell_xx_hide"
                :reserve-selection="true"
              ></el-table-column>
              <el-table-column width="50" v-if="type == 'radio'">
              </el-table-column>
              <el-table-column
                v-for="item in tableTitle"
                :key="item.props"
                :prop="item.props"
                :label="item.label"
                :width="item.width"
              >
                <template slot-scope="scope">
                  <div v-if="item.type === 'date'">
                    {{ getTime(scope.row[scope.column.property]) }}
                  </div>
                  <span v-else-if="item.props.indexOf('.') > -1">
                    {{
                      scope.row[item.props.split(".")[0]] == null
                        ? ""
                        : scope.row[item.props.split(".")[0]][
                            item.props.split(".")[1]
                          ]
                    }}
                  </span>
                  <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="sizes,prev,pager,next,jumper"
                :total="total"
                :page-size="search.page_size"
                :current-page="search.page"
                @current-change="currentChange"
                @size-change="sizeChange"
                :page-sizes="[10, 20, 50, 100]"
              ></el-pagination>
            </div>
          </div>
        </div>
        <div class="class-list--right">
          <div class="organization__title">
            <span
              >已选 物品<em>{{ right_goods_list.length }}</em
              >个</span
            >
            <span class="all-clear" @click="clear">
              <img src="../../assets/图片/icon_clear.png" alt="" />
              清空
            </span>
          </div>
          <div
            class="organization__info"
            v-for="(item, index) in right_goods_list"
            :key="index"
          >
            <span>{{ item.name }}</span>
            <img
              src="../../assets/图片/icon_close_green.png"
              alt=""
              @click="delOne(index, item.id)"
            />
          </div>
          <span v-if="right_goods_list.length === 0" class="is-empty"
            >暂无数据</span
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import goodsApi from "@/api/goods";
import timeFormat from "@/public/timeFormat";
export default {
  props: {
    type: {
      type: String,
      default: "multiple"
    },
    check_arr: Array,
    has_modal: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    check_arr: {
      handler(arr) {
        if (arr && arr.length) {
          console.log(arr);
          this.right_goods_list = arr;
          this.goodsName = arr.map((i) => i.name)?.join(",");
        } else {
          this.goodsName = "";
          this.right_goods_list = [];
          this.page = 1;
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      right_goods_list: [],
      isClear: false,
      page: 1,
      total: 0,
      searchTitle: [
        { props: "name", label: "物品名称", type: "input", show: true },
        {
          props: "course_id",
          label: "课程名称",
          type: "choose_course",
          show: false
        },
        {
          props: "is_enabled",
          label: "启用状态",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "启用", id: 1 },
            { name: "停用", id: 2 }
          ]
        },
        { props: "article_code", label: "物品代码", type: "input", show: false }
      ],
      search: {
        page: 1,
        page_size: 10,
        course_id: "",
        is_enabled: "",
        name: "",
        article_category_id: "",
        article_code: ""
      },
      rowSelectFlag: false,
      goodsName: "",
      goods_flag: false,
      goods_visible: false,
      tableData: [],
      tableTitle: [
        {
          props: "name",
          label: "物品名称",
          show: true,
          width: 200,
          fixed: true
        },
        {
          props: "article_category_name",
          label: "物品类别",
          show: true,
          width: 100
        },
        { props: "article_code", label: "物品代码", show: true, width: 100 },
        { props: "article_norms", label: "物品规格", show: true, width: 100 },
        { props: "is_enabled_str", label: "启用状态", show: true, width: 100 },
        {
          props: "allow_carry_over_str",
          label: "是否允许结转",
          show: true,
          width: 130
        },
        {
          props: "allow_refund_str",
          label: "是否允许退费",
          show: true,
          width: 130
        },
        { props: "purchase_price", label: "采购单价", show: true, width: 100 },
        { props: "sell_price", label: "销售单价", show: true, width: 100 },
        { props: "unit", label: "单位", show: true, width: 100 },
        { props: "course_name", label: "课程名称", show: true, width: 100 },
        { props: "memo", label: "备注", show: true, width: 100 }
      ]
    };
  },
  methods: {
    // 年月日
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    searchVal() {
      this.page = 1;
      this.search.page = 1;
      // this.clearSelection();
      this.getGoodsList();
    },
    reset() {
      this.search = {
        page: 1,
        page_size: 10,
        course_id: "",
        is_enabled: "",
        name: "",
        article_code: ""
      };
      this.page = 1;
      this.pageSize = 10;
      // this.clearSelection();
      this.searchVal();
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          console.log(this.check_arr);
          this.$refs.table.clearSelection();
          this.right_goods_list = [];
        });
      }
      this.isClear = true;
    },
    handleClose() {
      this.page = 1;
      this.goods_visible = false;
    },
    async getGoodsList() {
      this.loading = true;
      this.tableData = [];
      const res = await goodsApi.getGoodsList(this.search);
      const { count, results } = res.data;
      this.tableData = results == null ? [] : results;
      this.total = count;
      this.loading = false;
      this.$nextTick(() => {
        if (this.type === "radio") {
          this.right_goods_list.forEach((row) => {
            const find_row = this.tableData.find((item) => item.id === row.id);
            this.$refs.table.setCurrentRow(find_row);
          });
        } else {
          setTimeout(() => {
            this.rowSelectFlag = true;
            this.tableData.forEach((row) => {
              const find_index = this.right_goods_list.findIndex(
                (item) => item.id === row.id
              );
              this.$refs.table.toggleRowSelection(row, find_index !== -1);
            });
            this.rowSelectFlag = false;
          }, 0);
        }
      });
    },
    currentChange(val) {
      this.search.page = val;
      this.getGoodsList();
    },
    sizeChange(val) {
      this.page = 1;
      this.search.page_size = val;
      this.getGoodsList();
    },
    rowClassName({ row }) {
      if (this.is_charge) {
        const { student_numb, pre_enrolment_numb } = row;
        if (student_numb >= pre_enrolment_numb) {
          return "row-disabled";
        }
        return "";
      }
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();
      return row.id;
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    openGoodsDialog() {
      this.goods_visible = true;
      this.getGoodsList();
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag) return;
        const checkedPool = this.isClear
          ? [...val]
          : [...this.check_arr, ...val];
        const new_arr = [];
        checkedPool.forEach((i) => {
          if (!new_arr.some((j) => j.id === i.id)) {
            new_arr.push(i);
          }
        });
        this.right_goods_list = new_arr;
      }
    },
    async handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_goods_list = val == null ? [] : [val];
      }
    },
    async rowClick(row) {
      if (this.type === "radio") return false;
      const ids = this.right_goods_list.map((item) => item.id);
      const index = ids.indexOf(row.id);
      this.$refs.table.toggleRowSelection(row, index === -1);
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_goods_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.$nextTick(() => {
            this.rowSelectFlag = true;
            const id = this.right_goods_list[index].id;
            this.tableData.forEach((item) => {
              if (item.id === id) {
                this.$refs.table.toggleRowSelection(item, false);
              }
            });
            this.right_goods_list.splice(index, 1);
            this.rowSelectFlag = false;
          });
        }, 0);
      }
    },
    back() {
      this.goods_visible = false;
    },
    really() {
      const ids = [];
      const names = [];
      this.right_goods_list.forEach((item) => {
        ids.push(item.id);
        names.push(item.name);
      });
      this.goodsName = names.join(",");
      this.goods_visible = false;
      console.log(this.right_goods_list);

      // this.$emit(
      //   "update:check_id",
      //   this.right_goods_list.length > 0 ? ids.toString() : ""
      // );
      // this.$emit(
      //   "update:check_name",
      //   this.right_goods_list.length > 0 ? names.toString() : ""
      // );
      this.$emit(
        "update:check_arr",
        this.right_goods_list.length > 0 ? this.right_goods_list : []
      );
      this.$emit("confirm");
      // this.$emit("close");
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-dialog__body {
  padding: 0 16px 0 16px;
}
.tg-dialog__content {
  display: flex;
  flex-direction: row;
  height: 589px;
}
.class-list {
  width: calc(100% - 274px);
  border-right: 1px solid #e0e6ed;
  padding-right: 16px;
  .tg-table__box {
    margin-left: 0;
    margin-right: 16px;
  }
  ::v-deep .el-table {
    padding: 0;
    th {
      background: #f5f8fc;
    }
    .el-table__header {
      padding: 0 16px;
      background: #f5f8fc;
    }
    .el-table__body {
      padding: 0 16px;
    }
    tr.row-disabled {
      cursor: not-allowed;
      opacity: 0.5;
      background-color: #dfdede;
    }
  }
}
.class-list--right {
  width: 257px;
  margin-left: 16px;
  margin-top: 16px;
  height: 414px;
  overflow: auto;
  .organization__title,
  .organization__info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .all-clear {
    // color: #157df0;
    font-size: @text-size_small;
    font-family: @text-famliy_medium;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -3px;
    }
  }
  .organization__title {
    em {
      font-style: normal;
      color: @base-color;
    }
  }
  .organization__info {
    border: 1px solid @base-color;
    border-radius: 4px;
    height: 40px;
    align-items: center;
    padding: 0 16px;
    margin-top: 16px;
    ::v-deep .el-input,
    ::v-deep .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
    img {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
    span:nth-child(1) {
      overflow-x: scroll;
      width: calc(100% - 36px);
      white-space: nowrap;
    }
  }
  .required {
    &::before {
      content: "*";
      margin-right: 5px;
      color: #ff0317;
    }
  }
}
.is-empty {
  color: @text-color_third;
  width: 100%;
  display: inline-block;
  text-align: center;
  line-height: 350px;
}
</style>
