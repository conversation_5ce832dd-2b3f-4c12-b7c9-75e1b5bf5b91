<template>
  <div class="container sales-container" v-loading="loading">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="form"
      :showNum="4"
      :isExport="false"
      @reset="reset"
      @search="init"
      :loadingState="exportLoading"
      :searchLoadingState="searchLoading"
    ></tg-search>
    <div class="rate-list-container">
      <div v-for="(item, index) in rate_list" :key="index">
        <div class="label-container">
          <img :src="item.img_url" />
          <span>{{ item.label }}</span>
        </div>
        <div class="rate-content">{{ item.value }}</div>
      </div>
    </div>
    <!-- <chartTable1
      :type="'sales'"
      :chartData="data_obj"
      :searchDate="form.date_range"
    ></chartTable1> -->
  </div>
</template>
<script>
import frontApi from "@/api/front";
import { downLoadFile } from "@/public/downLoadFile";

// import chartTable1 from "./chartTable1.vue";
import quickTime from "@/public/quickTime";

export default {
  data() {
    return {
      searchLoading: false,
      form: {
        valid_status: undefined
      },
      isExport: true,
      rate_list: [
        {
          name: "invite_ratio",
          img_url: require("../../assets/图片/rate_invitation.png"),
          label: "邀约率",
          value: ""
        },
        {
          name: "reach_ratio",
          img_url: require("../../assets/图片/rate_in_store.png"),
          label: "到店率",
          value: ""
        },
        {
          name: "try_listen_ratio",
          img_url: require("../../assets/图片/rate_audition.png"),
          label: "试听率",
          value: ""
        },
        {
          name: "sign_order_ratio",
          img_url: require("../../assets/图片/rate_signing.png"),
          label: "签单率",
          value: ""
        },
        {
          name: "try_listen_to_ratio",
          img_url: require("../../assets/图片/rate_transform.png"),
          label: "试听转化率",
          value: ""
        }
      ],
      list: [],
      total: 0,
      detail_visible: false,
      page_size: 10,
      page: 1,
      data_obj: {},
      exportLoading: false,
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "valid_status",
          label: "客户有效性",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "待定", id: "pending" },
            { name: "有效", id: "valid" },
            { name: "无效", id: "invalid" }
          ]
        }
      ],
      loading: false,
      firstCome: true
    };
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId.toString();
    }
  },
  watch: {
    schoolIds: {
      handler(val) {
        if (this.firstCome) {
          this.setSearchDefault();
        }
        this.init();
      },
      immediate: true
    },
    "form.date_range": {
      handler(val) {
        this.firstCome = false;
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    init() {
      this.searchLoading = true;
      this.loading = true;
      const query = this.getSearch();
      frontApi.getCustomerTransform(query).then((res) => {
        if (+res.status === 200 && res.data) {
          this.data_obj = res.data.data;
          for (let i = 0; i < this.rate_list.length; i++) {
            const rate = this.rate_list[i];
            for (const key in this.data_obj) {
              if (rate.name === key) {
                this.rate_list[i].value = this.data_obj[key];
              }
            }
          }
          this.loading = false;
          this.searchLoading = false;
        }
      });
    },
    reset() {
      this.form = {
        valid_status: undefined
      };
      this.setSearchDefault();
    },
    getSearch() {
      const query = {
        valid_status: this.form.valid_status,
        // page,
        // page_size,
        department_id: this.schoolIds.split(",")
      };
      if (this.form.date_range && this.form.date_range.length) {
        const [start, end] = this.form.date_range;
        query.search_begin = start || "";
        query.search_end = end || "";
      }
      return query;
    },
    exportTable() {
      if (!this.table_list) {
        this.$message.warning("暂无可导出数据!");
      }
      this.exportLoading = true;
      const query = {
        department_id: this.schoolIds.split(",")
      };
      frontApi.exportCustomerIntention(query).then((res) => {
        downLoadFile(res, `销售数据`);
        this.exportLoading = false;
      });
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.form, "date_range", pastThirty);
    }
  }
  // components: { chartTable1 }
};
</script>
<style lang="less" scoped>
.container {
  margin: 0 !important;
  padding-top: 10px;
  box-sizing: border-box;
  .rate-list-container {
    width: 100%;
    height: 182px;
    padding: 32px 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 4px;
    > div {
      width: 186px;
      height: 100%;
      background: #f5f8fc;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      border-radius: 4px;
      padding: 16px 20px;
      box-sizing: border-box;
      font-weight: 400;

      .label-container {
        display: flex;
        align-items: center;
        img {
          width: 36px;
          height: 36px;
          margin-right: 10px;
        }
        span {
          font-size: 18px;
          color: #1f2d3d;
        }
      }
      .rate-content {
        font-size: 24px;
        line-height: 46px;
        color: #475669;
      }
    }
  }
}
.sales-container {
  padding-bottom: 16px;
}
</style>
