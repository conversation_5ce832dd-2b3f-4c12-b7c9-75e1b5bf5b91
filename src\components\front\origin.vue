<template>
  <div class="container">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="form"
      :showNum="1"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="init"
      :loadingState="exportLoading"
      :searchLoadingState="searchLoading"
    ></tg-search>
    <div class="echars_box" ref="categoryChar"></div>
    <el-row
      class="tg-box--margin tg-shadow--margin tg-row--height tg-box--width"
      v-if="is_sub"
    >
      <el-button
        type="primary"
        @click="open(1)"
        class="tg-button--primary"
        v-has="{ m: 'citation', o: 'create' }"
        >返回上级</el-button
      >
    </el-row>
    <div class="tg-table__box" :class="{ 'table-container': list.length > 0 }">
      <div class="tg-box--border"></div>
      <!-- :span-method="objectSpanMethod" -->
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        :row-key="getRows"
        class="tg-table"
        :tree-props="{ children: 'children' }"
        :summary-method="getSummaries"
        show-summary
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column label="一级渠道" align="left" prop="channel_id">
          <template slot-scope="scope">
            <div class="channel-table--inline">
              <span :class="{ 'tg-text--disabled': !scope.row.status }">{{
                scope.row.node === 0
                  ? scope.row.channel_name
                  : scope.row.sub_channel_name
              }}</span>
              <el-tag
                size="mini"
                :type="scope.row.node === 0 ? 'success' : 'warning'"
              >
                {{ scope.row.node === 0 ? "一级渠道" : "二级渠道" }}</el-tag
              >
            </div>
          </template>
          <!-- <template slot-scope="scope">
            <el-button
              v-if="scope.row.sub_channel_id"
              @click="open(2, scope.row.sub_channel_id)"
              class="tg-text--blue"
              type="text"
              >{{ scope.row.channel_name }}</el-button
            >
            <span v-else>{{ scope.row.channel_name }}</span>
          </template> -->
        </el-table-column>
        <!-- <el-table-column label="二级渠道" align="center" v-if="is_sub">
          <template slot-scope="scope">{{
            scope.row.sub_channel_name
          }}</template>
        </el-table-column> -->
        <el-table-column
          label="客户数量"
          prop="customer_number"
        ></el-table-column
        ><el-table-column
          label="比例"
          prop="ratio"
          align="left"
        ></el-table-column>
        <el-table-column label="转化成功" align="left" prop="to_student_number">
          <template slot-scope="scope">
            <el-button
              v-has="{ m: 'front', o: 'source_to_student_list' }"
              @click="openDialog(scope.row)"
              class="tg-text--blue"
              type="text"
              >{{ scope.row.to_student_number }}</el-button
            ></template
          ></el-table-column
        >
        <el-table-column label="转化率" align="left" prop="to_student_rate">
          <template slot-scope="scope">{{
            scope.row.to_student_rate
          }}</template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next,jumper"
          :total="total"
          :page-size="page_size"
          :current-page="page"
          @current-change="currentChange"
        ></el-pagination>
      </div> -->
    </div>
    <DetailDialog
      v-if="dialogVisible"
      :info="detail_obj"
      :searchDate="form.date_range"
      @close="dialogVisible = false"
    ></DetailDialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import frontApi from "@/api/front";
import { downLoadFile } from "@/public/downLoadFile";
import loading from "@/views/loading";
import DetailDialog from "./detailDialog.vue";
import quickTime from "@/public/quickTime";

export default {
  components: {
    loading,
    DetailDialog
  },
  data() {
    return {
      searchLoading: false,
      exportLoading: false,
      tab: 0,
      total: 0,
      page_size: 10,
      page: 1,
      is_sub: false,
      isCourseExport: false,
      isTeacherExport: false,
      dialogVisible: false,
      isExport: true,
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "valid_status",
          label: "客户有效性",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "待定", id: "pending" },
            { name: "有效", id: "valid" },
            { name: "无效", id: "invalid" }
          ]
        }
      ],
      form: {
        valid_status: undefined
      },
      echartsCtx: null,
      loading: false,
      school_tree_visible: false,
      schoolId: "",
      list: [],
      source_obj: {},
      detail_visible: false,
      search_level: 1,
      detail_obj: "",
      firstCome: true
    };
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId.toString();
    }
  },
  watch: {
    schoolIds: {
      handler(val) {
        if (this.firstCome) {
          this.setSearchDefault();
        }
        this.init();
      },
      immediate: true
    },

    "form.date_range": {
      handler(val) {
        this.firstCome = false;
      },
      deep: true
    }
  },
  created() {
    // for (let k = 0; k < this.tabList.length; k++) {
    //   if (this.$_has({ m: "addWechat", o: this.tabList[k].control })) {
    //     this.tab = k;
    //     break;
    //   }
    // }
    if (this.$_has({ m: "front", o: "source_export" })) {
      this.isExport = true;
    }
    // if (this.$_has({ m: "addWechat", o: "teacher_export" })) {
    //   this.isTeacherExport = true;
    // }
  },
  mounted() {
    // this.search_title = this.search_list[this.tab];
    // this.init();
  },
  methods: {
    open(val, id) {
      this.search_level = val;
      this.is_sub = val === 2;
      if (id) {
        this.form.source_id = id;
      }
      this.init();
    },
    openDialog(row) {
      console.log(row);
      const search_level = row?.node === 0 ? 1 : 2;
      this.dialogVisible = true;
      this.detail_obj = {
        search_level,
        name: search_level === 1 ? row.channel_name : row.sub_channel_name,
        source_id: search_level === 1 ? row.channel_id : row.sub_channel_id
      };
    },
    getSearch() {
      const { page, page_size } = this;
      const { source_id, date_range, valid_status } = this.form;
      const params = {
        page,
        page_size,
        department_id: this.schoolIds.split(","),
        source_id,
        valid_status
      };
      if (date_range && date_range.length) {
        const [start, end] = date_range;
        params.search_begin = start || "";
        params.search_end = end || "";
      }
      return params;
    },
    init() {
      this.list = [];
      this.loading = true;
      this.searchLoading = true;
      const query = this.getSearch();
      frontApi.getCustomerSource(query).then((res) => {
        if (res.data && res.data.data) {
          this.source_obj = res.data.data;
          const list = res.data.data.results ?? [];
          this.list = list.map((t) => {
            t.department_name = res.data.data.department_name;
            t.node = 0;
            return t;
          });
          this.loading = false;
          this.searchLoading = false;
          this.initEchart();
        } else {
          this.list = [];
          this.loading = false;
          this.searchLoading = false;
          this.initEchart();
        }
      });
    },
    reset() {
      this.form = {
        date_range: []
      };
      this.search_level = 1;
      this.setSearchDefault();
      this.init();
    },
    confirmSchool() {
      if (this.schoolId) {
        this.school_tree_visible = false;
        this.search_list[0][1].department_id = this.schoolId;
        this.search_list[1][1].department_id = this.schoolId;
        this.search();
      } else {
        this.$message.info("请勾选校区");
      }
    },
    exportExcel() {
      // 导出
      this.exportLoading = true;
      const query = this.getSearch();

      frontApi.exportCustomerSource(query).then((res) => {
        downLoadFile(res, `市场渠道列表`);
        this.exportLoading = false;
      });
    },
    // 合计加百分比
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        const values = data.map((item) => {
          return item[column.property];
        });
        let sum = 0;
        values.forEach((item) => {
          if (typeof item === "number") {
            sum = sum + item;
          } else {
            sum = "";
          }
        });
        const { customer_sum, to_student_rate, to_student_sum } =
          this.source_obj;
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 1) {
          sums[index] = customer_sum;
          return;
        }
        if (index === 3) {
          sums[index] = to_student_sum;
          return;
        }
        if (index === 4) {
          sums[index] = to_student_rate;
        }
      });
      return sums;
    },
    // objectSpanMethod({ rowIndex, columnIndex }) {
    //   if (columnIndex === 0) {
    //     const _row = this.flitterData(this.list).one[rowIndex];
    //     const _col = _row > 0 ? 1 : 0;
    //     return {
    //       rowspan: _row,
    //       colspan: _col
    //     };
    //   }
    // },
    flitterData(arr) {
      const spanOneArr = [];
      let concatOne = 0;
      arr.forEach((item, index) => {
        if (index === 0) {
          spanOneArr.push(1);
        } else {
          if (item.name === arr[index - 1].name) {
            spanOneArr[concatOne] += 1;
            spanOneArr.push(0);
          } else {
            spanOneArr.push(1);
            concatOne = index;
          }
        }
      });
      return {
        one: spanOneArr
      };
    },
    getRows(row) {
      if (row.sub_channel_id) {
        return `${row.channel_id}-${row.sub_channel_id}`;
      } else {
        return `${row.channel_id}`;
      }
    },
    initEchart() {
      this.echartsCtx = echarts.init(this.$refs.categoryChar);
      this.charOption();
      this.echartsCtx.getZr().on("click", (params) => {
        if (params.target && this.search_level === 1) {
          // 点击到柱状图柱子上的逻辑
          const pointInPixel = [params.offsetX, params.offsetY];
          let index;
          if (this.echartsCtx.containPixel("grid", pointInPixel)) {
            index = this.echartsCtx.convertFromPixel({ seriesIndex: 0 }, [
              params.offsetX,
              params.offsetY
            ])[0];
          }
          if (index !== undefined && this.list[index].sub_channel_id) {
            this.open(2, this.list[index].sub_channel_id);
          }
        }
      });
    },
    charOption() {
      const xData = [];
      const yData = [];
      this.list.map((t) => {
        if (this.search_level === 1) {
          xData.push(t.channel_name);
        } else {
          xData.push(t.sub_channel_name);
        }
        yData.push(t.customer_number);
      });
      const option = {
        title: {
          show: !this.list.length, // 无数据时展示 title
          textStyle: {
            color: "#8492a6",
            fontSize: 18,
            fontWeight: "normal"
          },
          text: "暂无数据～",
          left: "center",
          top: "center"
        },
        tooltip: {
          trigger: "item"
        },
        legend: {
          show: true,
          right: "1%",
          top: "10%"
        },
        dataset: {
          source: this.list
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: xData,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          splitNumber: 3,
          axisLabel: {
            show: true,
            interval: 0,
            formatter: "{value}"
          }
        },
        series: [
          {
            type: "bar",
            barWidth: "40%",
            // name: "加微率",
            data: yData,
            barMaxWidth: 60,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#07C7F1" },
                { offset: 0.4, color: "#0FB4E8" },
                { offset: 1, color: "#3272EE" }
              ])
            },
            label: {
              show: true,
              position: "top",
              valueAnimation: true,
              formatter: "{c}"
            }
          }
        ]
      };
      this.echartsCtx.setOption(option);
    },
    currentChange(val) {
      this.page = val;
      this.getList();
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.form, "date_range", pastThirty);
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  // height: calc(100% - 94px);
  width: 100%;
  overflow-y: auto;
  margin: 0 !important;
}
::v-deep .el-table td:last-child {
  border-right-color: transparent !important;
}
::v-deep .el-table__header th:last-child {
  border-right-color: transparent !important;
}
.tab {
  position: absolute;
  top: 46px;
  border-top: 1px solid #e0e6ed;
  left: 0;
  height: 46px;
  background: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  width: 100%;
  box-sizing: border-box;
  span {
    font-family: @text-famliy_semibold;
    color: @text-color_second;
    font-size: @text-size_normal;
    display: inline-block;
    height: 44px;
    border-bottom: 2px solid transparent;
    line-height: 44px;
    cursor: pointer;
    font-weight: bold;
  }

  span + span {
    margin-left: 32px;
  }

  span.refund-tab--active {
    color: #2d80ed;
    border-color: #2d80ed;
  }
}
.tg-search {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  padding-left: 16px;
  flex-shrink: 0;
  margin-bottom: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .tg-button__icon > span {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .tg-button__icon--large {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
}
.echars_box {
  width: 100%;
  height: 202px;
  background: #fff;
  margin-top: 14px;
}
.el-table {
  padding: 0;
}
.table-container {
  position: relative;
  &::after {
    content: "";
    width: 100%;
    display: inline-block;
    border: 1px solid #2d80ed;
    height: 44px;
    border-radius: 4px;
    position: absolute;
    box-sizing: border-box;
    left: 0;
    bottom: 52px;
    z-index: 99;
  }
}
::v-deep .el-table__body {
  margin-bottom: 44px !important;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
/deep/ .el-table__footer-wrapper {
  position: absolute;
  left: 0;
  height: 44px;
  border-radius: 4px;
  bottom: 0;
  // padding: 0 16px;
  box-sizing: border-box;
  &::after {
    width: 100%;
    height: 44px;
    content: "";
    box-sizing: border-box;
    border-radius: 4px;
    position: absolute;
    bottom: 0 !important;
    left: 0;
    z-index: 10;
    pointer-events: none;
    box-shadow: 0 2px 0 0 #ebf4ff;
  }
  table {
    height: 100%;
    padding: 0 15px;
  }
  tbody {
    td {
      background: #fff;
      .cell {
        z-index: 99;
      }
    }
  }
}

.channel-table--inline {
  display: inline-block;
  span + .el-tag {
    margin-left: 10px;
  }
}
</style>
