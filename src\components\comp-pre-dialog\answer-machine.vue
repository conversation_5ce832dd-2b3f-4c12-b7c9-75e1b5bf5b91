<template>
  <div class="mask" :style="{ 'z-index': Zindex }">
    <div class="answer_box_wrap">
      <VueDragResize
        :h="'auto'"
        :w="'auto'"
        :isActive="true"
        :isDraggable="true"
        :isResizable="false"
        :y="(screenHeight * 1) / 4"
        :x="(screenWidth * 1) / 4"
        v-on:resizing="resize"
        v-on:dragging="resize"
        :parentLimitation="true"
      >
        <div class="answer_content">
          <div class="answer_content_left">
            <p class="title">答题器</p>
            <div class="options_wrap">
              <p class="time">{{ s_to_hs(time) }}</p>
              <div class="line"></div>
              <div class="options">
                <div
                  class="op"
                  v-for="(item, index) in optionsList"
                  :key="index"
                  :class="{ active: index === checkOptionsIndex }"
                  @click="getOption(index, item)"
                >
                  {{ item.code }}
                </div>
                <div style="display: flex" v-if="isShowAdd">
                  <div
                    class="tool all"
                    :class="{ check: checkAdd }"
                    @click="
                      addQuestion();
                      checkAdd = true;
                    "
                  >
                    +
                  </div>
                  <div
                    class="tool jian"
                    :class="{ check: !checkAdd }"
                    @click="
                      jianQuestion();
                      checkAdd = false;
                    "
                  >
                    -
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="answer_content_right">
            <p class="title">统计</p>
            <div class="right_content">
              <div class="content">
                <div class="echarts">
                  <answerMachineEcharts
                    :questionBankAnswerObj="questionMachineObj"
                    :allNum="allNum"
                  ></answerMachineEcharts>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="liveAudit !== 'Audit'"
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 3vh;
          "
        >
          <div class="button_wrap" style="width: 19vh">
            <!-- <div style="display: flex"> -->
            <!-- && questionMachineObj?.answer_num > 0 -->
            <div
              class="trophy"
              @click="sendTrophyAll()"
              v-loading="trophyAllLoad"
            ></div>
            <!-- && questionMachineObj?.answer_num > 0 -->
            <div
              class="trophy right"
              @click="sendTrophyRight()"
              v-loading="trophyRightLoad"
            ></div>
          </div>

          <div class="button_wrap">
            <div
              class="setting_answer"
              @click="setAnswer"
              v-if="isShowAdd"
            ></div>
            <div class="finsh_answer" v-throttle="() => finsh()"></div>
          </div>
        </div>
        <img
          v-if="!isShowAdd && liveAudit !== 'Audit'"
          src="@/assets/living/最小化按钮.png"
          alt=""
          class="more"
          @click="more"
        />
      </VueDragResize>
    </div>
  </div>
</template>
<script>
import config from "@/config";
import livePage from "@/api/livePage";
import answerMachineEcharts from "./answer-machine-echarts";
import VueDragResize from "vue-drag-resize";

export default {
  data() {
    return {
      right: "",
      checkOptionsIndex: -1,
      optionsList: [],
      i: 0,
      checkAdd: true,
      time: 0,
      timer: null,
      question_id: "",
      allNum: 0,
      answerNum: 0,
      isShowAdd: true,
      screenHeight: 0,
      screenWidth: 0,
      Zindex: 10,
      superTeachFirstReceive: false,
      trophyAllLoad: false,
      trophyRightLoad: false,
      allTropTim: null,
      rightTropTim: null
    };
  },
  props: {
    questionMachineObj: {
      type: Object
    },
    liveAudit: String
  },
  watch: {
    questionMachineObj: {
      handler(val) {
        if (!val.option || val.option.length === 0) return;
        this.$nextTick(() => {
          this.isShowAdd = false;
          if (
            val.after_time > 0 ||
            (!this.superTeachFirstReceive && this.liveAudit === "Audit")
          ) {
            this.time = val.after_time;
            this.reGetCountUp();
            this.superTeachFirstReceive = true;
          }
          this.optionsList = [];
          val.option.forEach((item, index) => {
            this.optionsList[index] = {};
            this.optionsList[index].code = item;
          });
          if (val.correct_answer) {
            this.checkOptionsIndex = val.option.findIndex(
              (item) => item === val.correct_answer
            );
            this.right = val.correct_answer;
          }
        });
      },
      deep: true,
      immediate: true
    }
  },
  computed: {},
  components: { answerMachineEcharts, VueDragResize },
  mounted() {
    this.$eventBus.$on("show_small_statistics", (obj) => {
      if (obj.type === 2) {
        this.setOpacityMin();
      }
    });
    this.$eventBus.$on("showMinimizeMore", (obj) => {
      if (obj.type === 2) {
        if (obj.mark === "show") {
          this.setOpacity10();
        } else if (obj.mark === "delete") {
          this.finsh();
        }
      }
    });
    this.$eventBus.$on("deleteMinimize", (obj) => {
      this.finsh();
    });
    for (let i = 0; i < 2; i++) {
      this.addQuestion();
    }
  },
  created() {
    this.screenHeight = document.body.clientHeight;
    this.screenWidth = document.body.clientWidth;
  },
  methods: {
    sendTrophyAll() {
      if (this.isShowAdd) {
        this.$message.info("请点击开始答题");
      }
      if (!("answer_num" in this.questionMachineObj)) return;
      if (this.questionMachineObj.answer_num === 0) return;
      if (this.trophyAllLoad) {
        this.$message.warning("奖杯冷却中");
      } else {
        this.trophyAllLoad = true;
        this.allTropTim = setTimeout(() => {
          this.trophyAllLoad = false;
          this.allTropTim = null;
        }, 3000);
        this.$eventBus.$emit("sendTrophy", {
          sendTrophy: "answerAll",
          type: "trophy"
        });
        if (!this.isShowAdd) {
          this.$message.success("奖杯发送成功");
        }
      }
    },
    sendTrophyRight() {
      if (this.isShowAdd) {
        this.$message.info("请点击开始答题");
      }
      if (!("answer_num" in this.questionMachineObj)) return;
      if (this.questionMachineObj.answer_num === 0) return;

      if (this.trophyRightLoad) {
        this.$message.warning("奖杯冷却中");
      } else {
        this.trophyRightLoad = true;
        this.rightTropTim = setTimeout(() => {
          this.trophyRightLoad = false;
          this.rightTropTim = null;
        }, 3000);
        this.$eventBus.$emit("sendTrophy", {
          sendTrophy: "answerRight",
          type: "trophy"
        });
        if (!this.isShowAdd) {
          this.$message.success("奖杯发送成功");
        }
      }
    },
    s_to_hs: function (value) {
      let secondTime = parseInt(value); // 秒
      let minuteTime = 0; // 分
      let hourTime = 0; // 时
      if (secondTime > 60) {
        // 如果秒数大于60，将秒数转换成整数
        // 获取分钟，除以60取整，得到整数分钟
        minuteTime = parseInt(secondTime / 60);
        // 获取秒数，秒数取余，得到整数秒数
        secondTime = parseInt(secondTime % 60);
        // 如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          // 获取小时，获取分钟除以60，得到整数小时
          hourTime = parseInt(minuteTime / 60);
          // 获取小时后取余的分，获取分钟除以60取余的分
          minuteTime = parseInt(minuteTime % 60);
        }
      }
      // 若秒数是个位数，前面用0补齐
      secondTime = secondTime < 10 ? "0" + secondTime : secondTime;
      let result = "" + secondTime + "";
      if (minuteTime > 0) {
        // 若分钟数是个位数，前面用0补齐
        minuteTime = minuteTime < 10 ? "0" + minuteTime : minuteTime;
        result = "" + minuteTime + ":" + result;
      } else {
        // 若分钟数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }

      if (hourTime > 0) {
        // 若小时数是个位数，前面用0补齐
        hourTime = hourTime < 10 ? "0" + hourTime : hourTime;
        result = "" + hourTime + ":" + result;
      } else {
        // 若小时数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }
      // console.log("result", result);
      return result;
    },
    reGetCountUp() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        this.time++;
      }, 1000);
    },
    setAnswer() {
      // if (this.right === "") {
      //   this.$message.info("请设置正确答案");
      //   return;
      // }
      const newArr = [];
      this.optionsList.forEach((item) => {
        newArr.push(item.code);
      });
      const obj = {
        answer: this.right,
        option: newArr,
        room_id: parseInt(config.roomId)
      };
      livePage.machineAnswer(obj).then((res) => {
        if (res.data.code === 0) {
          this.isShowAdd = false;
          this.allNum = res.data.data.num;
          this.questionMachineObj.question_id = res.data.data.question_id;
          localStorage.setItem("answer_question_id", res.data.data.question_id);
          this.reGetCountUp();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    async finsh() {
      if (this.questionMachineObj?.question_id) {
        const res = await livePage.finashMachineAnswer({
          question_id: this.questionMachineObj.question_id,
          room_id: parseInt(config.roomId)
        });
        console.log(res);
        this.$eventBus.$emit("show_small_statistics", {
          status: false,
          type: 2
        });
      }
      this.$emit("closeMachine");
    },
    getOption(index, item) {
      if (this.isShowAdd) {
        this.checkOptionsIndex = index;
        this.right = item.code;
      }
    },
    setDesc() {
      const letterArr = [];
      for (let i = 65; i < 91; i++) {
        letterArr[i] = String.fromCharCode(i);
      }
      return letterArr;
    },
    addQuestion() {
      if (this.i > 7) {
        return;
      }
      const randomAbc = this.setDesc().splice(65);
      // console.log(randomAbc, this.i);
      const res = [
        ...this.optionsList,
        {
          code: randomAbc[this.i]
        }
      ];
      this.i++;
      this.optionsList = res;
    },
    jianQuestion() {
      if (this.i > 2) {
        this.i--;
        this.optionsList.splice(this.optionsList.length - 1, 1);
        if (this.i === this.checkOptionsIndex) {
          this.checkOptionsIndex = -1;
        }
      }
    },
    resize(newRect) {
      this.width = newRect.width;
      this.height = newRect.height;
      this.top = newRect.top;
      this.left = newRect.left;
    },
    setOpacity10() {
      this.Zindex = 12;
    },
    setOpacityMin() {
      this.Zindex = -2;
    },
    more() {
      this.$eventBus.$emit("show_small_statistics", {
        status: true,
        type: 2,
        on: true
      });
      // this.$emit("colseStatisticsDialog");
    }
  },
  beforeDestroy() {
    this.$eventBus.$off("deleteMinimize");
  }
};
</script>
<style lang="less" scoped>
.mask {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  position: absolute;
  z-index: 9;
}
.answer_box_wrap {
  // display: flex;
  // justify-content: space-between;
  // align-items: flex-start;
  // max-width: 1500px;
  width: 100vw;
  height: 100vh;
  // position: absolute;
  // left: 0;
  // top: 0;
  // right: 0;
  // bottom: 0;
  // margin: auto;
  // border-radius: 25px;
  // background: #fff;
  // box-shadow: 0px 12px 40px 0px rgba(0, 0, 0, 0.1),
  //   0px -8px 30px 0px #ccfaff inset;
  // padding: 4vh 0;
  .answer_content {
    width: 100%;
    height: 45vh;
    border-radius: 25px;
    display: flex;
    .answer_content_left {
      width: 50%;
      height: 100%;
      padding: 0 3vh;
      box-sizing: border-box;
      .title {
        color: #333;
        text-align: center;
        font-size: 3vh;
        font-style: normal;
        font-weight: 600;
        margin-bottom: 3vh;
      }
      .options_wrap {
        width: 100%;
        min-height: 27vh;
        border-radius: 20px;
        background: #f6f8fb;
        padding: 2vh;
        box-sizing: border-box;
        .time {
          color: #666;
          text-align: center;
          font-size: 2vh;
          font-style: normal;
          font-weight: 500;
          margin-bottom: 1vh;
        }
        .line {
          width: 100%;
          height: 1px;
          background-color: #e1e1e1;
        }
        .options {
          width: 100%;
          display: flex; /* flex布局 */
          justify-content: flex-start; /* 左对齐 */
          flex-wrap: wrap; /* 换行 */
          margin-top: 1.5vh;
          .op {
            width: 7vh;
            height: 7vh;
            background: #fff;
            border: 2px solid #d8d8d8;
            box-shadow: 0px 0px 10px rgba(255, 163, 16, 0.1);
            border-radius: 50%;
            color: #d8d8d8;
            text-align: center;
            font-size: 4.5vh;
            font-style: normal;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 2.1vh;
            margin-bottom: 1.5vh;
            cursor: pointer;
          }
          .tool {
            width: 3vh;
            height: 3vh;
            // line-height: 2vh;
            background: #fff;
            border: 2px solid #d8d8d8;
            box-shadow: 0px 0px 10px rgba(255, 163, 16, 0.1);
            border-radius: 50%;
            color: #d8d8d8;
            text-align: center;
            font-size: 3vh;
            font-style: normal;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 0.5vh;
            cursor: pointer;
            padding-bottom: 3px;
          }
          .add {
            width: 2vh;
            height: 2vh;
          }
          .op:nth-child(4n) {
            margin-right: 0;
          }
          .active {
            background: linear-gradient(180deg, #30cdff 0%, #2eb1ff 100%);
            border: 3px solid #fff;
            box-shadow: 0px 8px 10px rgba(51, 51, 51, 0.1);
            color: #ffffff;
          }
          .check {
            border: 2px solid #29b1fc;
            color: #29b1fc;
          }
        }
      }
    }

    .answer_content_right {
      width: 50%;
      height: 100%;
      //   background-color: pink;
      //   padding: 0 3vh;
      //   box-sizing: border-box;
      .title {
        color: #333;
        text-align: center;
        font-size: 3vh;
        font-style: normal;
        font-weight: 600;
        margin-bottom: 1.5vh;
      }
      .right_content {
        width: 100%;
        // height: 33vh;
        // background-color: green;
        // overflow-x: scroll;

        .content {
          width: 100%;
          display: flex;
        }
        .echarts {
          width: 42.5vh;
          height: 38vh;
          //   background-color: orange;
          float: left;
          padding: 0 1vh;
          position: relative;
        }
        // .list {
        //   width: 40vh;
        //   height: 38vh;
        //   background-color: blue;
        //   float: left;
        // }
      }
    }
  }

  .button_wrap {
    width: 40vh;
    display: flex;
    justify-content: center;
    .setting_answer {
      width: 20vh;
      height: 8vh;
      background-image: url("https://tg-dev.oss-cn-beijing.aliyuncs.com/6b44b794-e2d2-4e8c-b33f-eaf5e2c3818d.png");
      background-size: contain;
      cursor: pointer;
    }
    .finsh_answer {
      width: 20vh;
      height: 8vh;
      background-image: url("https://tg-dev.oss-cn-beijing.aliyuncs.com/1fafb082-3b9e-4dc1-9f40-46b5ab1ea967.png");
      background-size: contain;
      cursor: pointer;
    }
  }

  .trophy {
    width: 68px;
    height: 68px;
    border-radius: 50%;
    background-image: url("https://tg-dev.oss-cn-beijing.aliyuncs.com/13c330fb-0563-44ac-8558-b66c03283c0a.png");
    background-size: contain;
    box-shadow: 0 4px 4px 0 #fee4bf;
    cursor: pointer;
  }
  .right {
    background-image: url("https://tg-dev.oss-cn-beijing.aliyuncs.com/8a96de8d-4f50-4805-8496-9d77bd42c9f6.png");
    margin-left: 16px;
  }
}

/deep/.content-container {
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1500px;
  width: 90vh !important;
  height: 60vh !important;
  margin: auto;
  border-radius: 25px;
  background: #fff;
  box-shadow: 0px 12px 40px 0px rgba(0, 0, 0, 0.1),
    0px -8px 30px 0px #ccfaff inset;
  padding: 4vh 0;
}
.more {
  position: absolute;
  width: 6vh;
  height: 6vh;
  top: 0vh;
  right: -8vh;
  cursor: pointer;
}
</style>
