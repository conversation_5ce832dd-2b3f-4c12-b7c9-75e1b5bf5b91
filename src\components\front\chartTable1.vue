<template>
  <div class="rule-container">
    <div class="left-container">
      <div class="label">{{ label_list[type].leftLabel }}</div>
      <div class="date-container" v-if="searchDate === null">不限</div>

      <div class="date-container" v-else>
        {{ searchDate[0] }}~{{ searchDate[1] }}
      </div>
      <div class="chart-box">
        <div ref="chart" style="width: 100%; flex: 1"></div>
      </div>
    </div>
    <div class="right-container">
      <div class="label">{{ label_list[type].rightLabel }}</div>
      <div class="date-container" v-if="searchDate === null">不限</div>

      <div class="date-container" v-else>
        {{ searchDate[0] }}~{{ searchDate[1] }}
      </div>
      <div class="tg-box--width">
        <div class="tg-table__box tg-box--margin table-container">
          <el-table
            ref="table"
            :data="tableData"
            tooltip-effect="dark"
            class="tg-table"
            :summary-method="getSummaries"
            :show-summary="type === 'rule' || type === 'sales'"
            :header-cell-style="{ 'background-color': '#F5F8FC' }"
            v-loading="false"
          >
            <!-- <el-table-column min-width="60" label="序号" prop="index" fixed>
          </el-table-column>-->
            <template v-for="(item, index) in table_title">
              <el-table-column
                v-if="item.show"
                :key="index"
                :prop="item.props"
                :label="item.label"
                show-overflow-tooltip
                align="center"
              >
                <template slot-scope="scope">
                  <el-button
                    v-has="{ m: 'inventoryQuery', o: 'detail' }"
                    v-if="item.props === 'article_name'"
                    @click="open(scope.row)"
                    class="tg-text--blue"
                    type="text"
                    >{{ scope.row.article_name }}</el-button
                  >
                  <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
              </el-table-column>
            </template>
            <!-- <template slot="empty">
            <div style="margin-top: 15%">
              <loading v-if="loading"></loading>
              <div v-else>暂无数据～</div>
            </div>
          </template> -->
          </el-table>
          <!-- <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="prev, pager, next,jumper"
                :total="total"
                :page-size="10"
                @current-change="currentChange"
              >
              </el-pagination>
            </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { option_obj, label_list_obj, table_list } from "./staticData";
export default {
  data() {
    return {
      option: option_obj,
      label_list: label_list_obj,
      table_list,
      table_title: {},
      cus_echarts: ""
    };
  },
  props: {
    type: {
      type: String,
      default: "rule"
    },
    chartData: {
      type: Object
    },
    tableData: {
      type: Array
    },
    searchDate: {
      type: Array
    }
  },
  watch: {
    chartData: {
      handler(val) {
        if (val) {
          this.cus_echarts = echarts.init(this.$refs.chart);
          const option = this.initOption();
          this.cus_echarts.setOption(option);
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.table_title = this.table_list[this.type];
  },
  methods: {
    initOption() {
      if (this.type === "rule" && this.chartData) {
        this.option[this.type].series[0].data = this.chartData.data || [];
        this.option[this.type].graphic = [
          {
            type: "text",
            left: "center",
            top: "center",
            style: {
              rich: {
                a: {
                  fontSize: 14,
                  lineHeight: 18
                },
                b: {
                  fontSize: 30,
                  textAlign: "center"
                }
              },
              fill: "#475669",
              text: [
                `{a|客户总数量(人)}\n{b|${this.chartData.total || 0}}`
              ].join("")
            }
          }
        ];
      }
      if (this.type === "sales" && this.chartData) {
        const {
          customer_number,
          promise_visit_number,
          real_visit_number,
          to_student_number,
          try_listen_number
        } = this.chartData;
        const data = [
          { value: customer_number, name: "当前获客数" },
          { value: promise_visit_number, name: "诺到访人数" },
          { value: real_visit_number, name: "到访人数" },
          { value: try_listen_number, name: "试听人数" },
          { value: to_student_number, name: "转化人数" }
        ];
        this.option[this.type].series[0].data = data;
        this.tableData = data;
        this.salesTotal =
          customer_number +
          promise_visit_number +
          real_visit_number +
          try_listen_number +
          to_student_number;
      }

      return this.option[this.type];
    },
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 1) {
          if (this.type === "rule") {
            sums[index] = this.chartData.total;
          } else {
            sums[index] = this.salesTotal;
          }

          return;
        }
        if (index === 2) {
          sums[index] = "100%";
        }
      });
      return sums;
    }
  }
};
</script>
<style lang="less" scoped>
.rule-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 10px;
  > div {
    flex: 50%;
    border-radius: 4px;
    background: #fff;
    padding: 16px;
    min-height: 460px;
    box-sizing: border-box;
  }
  .left-container {
    margin-right: 16px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }
  .right-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    .tg-box--width {
      position: relative;
      width: 100%;
      flex: auto;
      ::v-deep .tg-table__box {
        box-shadow: none;
        .el-table {
          &::before {
            height: 0px !important;
          }
        }
      }

      .table-container {
        height: 100%;
        position: absolute;
        margin: 0;
        ::v-deep .el-table {
          padding: 0;
        }
      }
    }
  }
  .label {
    border-left: 2px solid #2d80ed;
    padding-left: 6px;
    font-size: 14px;
    line-height: 20px;
    color: #1f2d3d;
    margin-bottom: 10px;
  }
  .date-container {
    color: #8492a6;
    line-height: 18px;
    font-size: 12px;
    margin-bottom: 16px;
  }
  ::v-deep.el-table__body-wrapper {
    margin-bottom: 44px !important;
  }
  .chart-box {
    display: flex;
    flex: auto;
    width: 100%;
    padding: 16px;
    box-sizing: border-box;
  }
  /deep/ .el-table__footer-wrapper {
    position: absolute;
    left: 0;
    height: 44px;
    border-radius: 4px;
    bottom: 0;
    &::after {
      width: calc(100%);
      height: 44px;
      content: "";
      border: 1px solid #2d80ed;
      border-radius: 4px;
      position: absolute;
      padding: 0 16px;
      background: #ebf4ff;
      bottom: 0;
      left: 0;
      z-index: 10;
      box-sizing: border-box;
      pointer-events: none;
      box-shadow: 0 2px 0 0 #ebf4ff;
    }
    tbody {
      td {
        background: #ebf4ff;
        .cell {
          z-index: 99;
          position: relative;
        }
      }
    }
  }
}
</style>
