<template>
  <div class="rule1-container">
    <div class="left-container">
      <div class="label">{{ label_list[type].leftLabel }}</div>
      <div class="date-container" v-if="searchDate === null">不限</div>

      <div class="date-container" v-else>
        {{ searchDate[0] }}~{{ searchDate[1] }}
      </div>
      <div
        style="flex: auto; width: 100%; padding: 16px; box-sizing: border-box"
      >
        <div ref="chart" style="height: 300px; width: 100%" id="mychart"></div>
      </div>
    </div>
    <div class="right-container">
      <div class="label">{{ label_list[type].rightLabel }}</div>
      <div class="date-container" v-if="searchDate === null">不限</div>

      <div class="date-container" v-else>
        {{ searchDate[0] }}~{{ searchDate[1] }}
      </div>
      <div class="tg-box--width">
        <div class="tg-table__box tg-box--margin table-container">
          <el-table
            ref="table"
            :data="newData"
            tooltip-effect="dark"
            class="tg-table"
            :show-summary="type === 'rule'"
            :header-cell-style="{ 'background-color': '#F5F8FC' }"
            v-loading="false"
          >
            <template v-for="(item, index) in table_title">
              <el-table-column
                v-if="item.show"
                :key="index"
                :prop="item.props"
                :label="item.label"
                show-overflow-tooltip
                align="center"
              >
                <template slot-scope="scope">
                  <div
                    v-if="item.props === 'intention_id'"
                    style="display: flex; justify-content: center"
                  >
                    <div class="el-rate" style="align-items: center">
                      <div
                        class="el-rate__item"
                        v-for="(item, i) in 5"
                        :key="i"
                      >
                        <i
                          class="el-rate__icon"
                          :class="
                            Number(scope.row.intention_id) - 1 >= i
                              ? 'el-icon-star-on'
                              : 'el-icon-star-off'
                          "
                        ></i>
                      </div>
                    </div>
                  </div>
                  <el-button
                    v-has="{ m: 'front', o: 'transfer_to_student_list' }"
                    v-if="item.props === 'transfer_count'"
                    @click="openDialog(scope.row)"
                    class="tg-text--blue"
                    type="text"
                    >{{ scope.row.transfer_count }}</el-button
                  >
                  <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
              </el-table-column>
            </template>
            <template slot="empty">
              <div style="margin-top: 15%">
                <loading v-if="loading"></loading>
                <div v-else>暂无数据～</div>
              </div>
            </template>
          </el-table>
        </div>
      </div>
    </div>
    <DetailDialog
      v-if="dialogVisible"
      :info="detail_obj"
      :requestData="requestData"
      @close="dialogVisible = false"
    ></DetailDialog>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { option_obj, label_list_obj, table_list } from "./staticData";
import DetailDialog from "./detailDialog1.vue";

export default {
  data() {
    return {
      option: option_obj,
      label_list: label_list_obj,
      table_list,
      table_title: {},
      cus_echarts: "",
      myChartStyle: { float: "left", width: "100%", height: "800px" }, // 图表样式
      dialogVisible: false,
      loading: true,
      receive_status: ""
    };
  },
  components: { DetailDialog },
  props: {
    type: {
      type: String,
      default: "rule"
    },
    newData: {
      type: Array
    },
    searchDate: {
      type: Array
    },
    requestData: {
      type: Object
    }
  },
  watch: {
    newData: {
      handler(val) {
        if (val.length === 0) {
          this.loading = false;
        } else {
          this.initOption();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initOption();
    this.table_title = this.table_list[this.type];
  },
  methods: {
    openDialog(row) {
      if (row.customer_status === "未到店") {
        this.receive_status = 4;
      } else {
        this.receive_status = 7;
      }
      this.requestData.receive_status = this.receive_status;
      this.dialogVisible = true;
      this.detail_obj = {};
    },
    initOption() {
      if (this.type === "transform" && this.newData.length > 0) {
        this.xData = [];
        this.yData = [];
        this.taskDate = [];
        this.newData.forEach((item) => {
          this.xData.push(item.customer_status);
          this.yData.push(item.flow_count);
          this.taskDate.push(item.transfer_count);
        });
        this.initEcharts();
      }
    },
    initEcharts() {
      // 多列柱状图
      const mulColumnZZTData = {
        xAxis: {},
        // 图例
        legend: {
          data: ["已跟进人数", "转化人数"],
          show: true,
          // type: "plain",
          // left: "right",
          top: "0"
          // width: 130
        },
        yAxis: {
          data: this.xData
        },
        series: [
          {
            type: "bar", // 形状为柱状图
            data: this.yData,
            name: "已跟进人数", // legend属性
            label: {
              // 柱状图上方文本标签，默认展示数值信息
              show: true,
              position: "right"
            },
            backgroundStyle: {
              color: "#2D80ED"
            },
            itemStyle: {
              normal: {
                color: "#2D80ED"
              }
            },
            barWidth: 16
          },
          {
            type: "bar", // 形状为柱状图
            data: this.taskDate,
            name: "转化人数", // legend属性
            label: {
              // 柱状图上方文本标签，默认展示数值信息
              show: true,
              position: "right"
            },
            backgroundStyle: {
              color: "#12C4C6"
            },
            itemStyle: {
              normal: {
                color: "#12C4C6"
              }
            },
            barWidth: 16
          }
        ]
      };
      const myChart = echarts.init(document.getElementById("mychart"));
      myChart.setOption(mulColumnZZTData);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    resize() {
      const myChart = echarts.init(document.getElementById("mychart"));
      myChart.resize();
    }
  }
};
</script>
<style lang="less" scoped>
.rule1-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 10px;
  .left-container {
    margin-right: 16px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    flex: 50%;
    border-radius: 4px;
    background: #fff;
    padding: 16px;
    min-height: 460px;
    box-sizing: border-box;
  }
  .right-container {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    flex: 50%;
    border-radius: 4px;
    background: #fff;
    padding: 16px;
    min-height: 460px;
    box-sizing: border-box;
    .tg-box--width {
      position: relative;
      width: 100%;
      flex: auto;
      ::v-deep .tg-table__box {
        box-shadow: none;
        .el-table {
          &::before {
            height: 0px !important;
          }
        }
      }

      .table-container {
        height: 100%;
        position: absolute;
        margin: 0;
        ::v-deep .el-table {
          padding: 0;
        }
      }
    }
  }
  .label {
    border-left: 2px solid #2d80ed;
    padding-left: 6px;
    font-size: 14px;
    line-height: 20px;
    color: #1f2d3d;
    margin-bottom: 10px;
  }
  .date-container {
    color: #8492a6;
    line-height: 18px;
    font-size: 12px;
    margin-bottom: 16px;
  }
  ::v-deep.el-table__body-wrapper {
    margin-bottom: 44px !important;
  }
  /deep/ .el-table__footer-wrapper {
    position: absolute;
    left: 0;
    height: 44px;
    border-radius: 4px;
    bottom: 0;
    &::after {
      width: calc(100%);
      height: 44px;
      content: "";
      border: 1px solid #2d80ed;
      border-radius: 4px;
      position: absolute;
      padding: 0 16px;
      background: #ebf4ff;
      bottom: 0;
      left: 0;
      z-index: 10;
      box-sizing: border-box;
      pointer-events: none;
      box-shadow: 0 2px 0 0 #ebf4ff;
    }
    tbody {
      td {
        background: #ebf4ff;
        .cell {
          z-index: 99;
          position: relative;
        }
      }
    }
  }
}
.el-rate {
  margin-left: 5%;
}
::v-deep .el-rate__icon.el-icon-star-off {
  background: url("../../assets/图片/icon_rate.png");
  background-size: 100%;
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
}
::v-deep .el-icon-star-off:before {
  content: "1";
  visibility: hidden;
}
::v-deep .el-rate__icon.el-icon-star-on {
  background: url("../../assets/图片/icon_rate_ac.png");
  background-size: 100%;
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
}
::v-deep .el-icon-star-on:before {
  content: "1";
  visibility: hidden;
}
</style>
