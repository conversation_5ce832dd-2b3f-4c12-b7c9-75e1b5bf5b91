<!--选择教辅包-->
<template>
  <el-dialog
    :visible="true"
    title="选择教辅包"
    width="1000px"
    :before-close="handleClose"
    class="choose-goods-package"
    :modal="has_modal"
    :append-to-body="true"
  >
    <div class="tg-dialog__content">
      <div class="class-list">
        <div class="search tg-box--margin">
          <el-form :inline="true" :model="form">
            <el-form-item>
              <el-input
                placeholder="请输入教辅包名称"
                class="search__input"
                v-model="form.name"
                @keyup.enter.native="searchVal()"
              >
                <img
                  src="../../assets/图片/icon_search_grey.png"
                  alt=""
                  slot="prefix"
                />
                <!-- <img
                  :src="
                    !flag
                      ? require('../../assets/图片/icon_double_down.png')
                      : require('../../assets/图片/icon_double_up_ac.png')
                  "
                  alt=""
                  slot="suffix"
                  class="search__img"
                  @click="flag = !flag"
                /> -->
              </el-input>
            </el-form-item>
            <el-form-item class="tg-form--special">
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="searchVal"
              >
                <img
                  src="../../assets/图片/icon_search.png"
                  alt=""
                  class="tg-button__icon--normal"
                />查询
              </el-button>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="reset"
              >
                <img
                  src="../../assets/图片/icon_reset.png"
                  alt=""
                  class="tg-button__icon--normal"
                />重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="list"
            tooltip-effect="dark"
            class="tg-table"
            :height="flag ? 353 : 449"
            @selection-change="handleSelectionChange"
            @current-change="handleCurrentChange"
            @select="checkBoxSelect"
            :row-key="getRowKeys"
            :row-style="rowStyle"
            :highlight-current-row="type == 'radio' ? true : false"
            @row-click="rowClick"
          >
            <el-table-column
              v-if="type != 'radio'"
              type="selection"
              width="50"
              :selectable="checkSelectable"
              :reserve-selection="true"
              label-class-name="cell_xx_hide"
            ></el-table-column>
            <el-table-column
              width="20"
              v-if="type == 'radio'"
            ></el-table-column>
            <el-table-column label="教辅包名称" prop="name"></el-table-column>
            <el-table-column label="包含物品" prop="article_number">
              <template slot-scope="scope">
                <table-popover
                  :goodsNum="scope.row.article_number"
                  :goodsId="scope.row.id"
                  :is_charge="true"
                  :department_id="department_id"
                  width="800"
                  placement="right"
                >
                </table-popover>
              </template>
            </el-table-column>
            <el-table-column
              label="售卖价格"
              prop="package_sell_price"
              width="100"
            ></el-table-column>
            <el-table-column label="是否可售卖" prop="is_sell" width="150">
              <template slot-scope="scope">
                <span v-if="scope.row.is_sell == 1">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="total"
              :page-size="page_size"
              :current-page="page"
              @current-change="currentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="class-list--right">
        <div class="organization__title">
          <span
            >已选 教辅包<em>{{ right_goods_list.length }}</em
            >个</span
          >
          <span class="all-clear" @click="clear">
            <img src="../../assets/图片/icon_clear.png" alt="" />
            清空
          </span>
        </div>
        <div
          class="organization__info"
          v-for="(item, index) in right_goods_list"
          :key="index"
        >
          <span>{{ item.name }}</span>
          <img
            src="../../assets/图片/icon_close_green.png"
            alt=""
            @click="delOne(index, item.id)"
          />
        </div>
        <span v-if="right_goods_list.length === 0" class="is-empty"
          >暂无数据</span
        >
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >取消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import goodsApi from "@/api/goods";
import chargeApi from "@/api/charge";
export default {
  name: "chooseTeacherPackage",
  data() {
    return {
      right_goods_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      form: {
        name: ""
      },
      search_flag: false,
      rowSelectFlag: false,
      categroy_list: []
    };
  },
  props: {
    check_id: String,
    check_name: String,
    check_arr: {
      type: Array,
      default: () => []
    },
    has_modal: {
      type: Boolean,
      default: true
    },
    department_id: String,
    type: {
      type: String,
      default: "select"
    },
    status: {
      type: Boolean,
      default: false // true只显示启用的列表
    },
    // 学员信息
    stu_info: {
      type: Object,
      default: null
    },
    // 收费时是否校验学员能否买选中的课程
    check_can_buy: {
      type: Boolean,
      default: false
    },
    hide_header_checkbox: {
      type: Boolean,
      default: false
    },
    // 是否收费页面的选择教辅包
    is_charge: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    // 暂时隐藏全选，后续需要后台做处理
    if (this.hide_header_checkbox) {
      this.$nextTick(() => {
        // eslint-disable-next-line no-undef
        $(".choose-goods-package .cell_xx_hide.cell").hide();
      });
    }
    this.right_goods_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
    this.getCategroyList();
    this.getGoods({
      page: this.page,
      page_size: this.page_size,
      department_id: this.department_id,
      ...this.form
    });
  },
  methods: {
    rowStyle({ row }) {
      if (row.is_sell === 2) {
        return { color: "#C0CCDA", cursor: "not-allowed" };
      }
    },
    checkSelectable(row) {
      return row.is_sell === 1;
    },
    handleClose() {
      this.$emit("close");
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_goods_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.rowSelectFlag = true;
          const id = this.right_goods_list[index].id;
          this.list.forEach((item) => {
            if (item.id === id) {
              this.$refs.table.toggleRowSelection(item, false);
            }
          });
          this.right_goods_list.splice(index, 1);
          this.rowSelectFlag = false;
        }, 0);
      }
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.table.clearSelection();
          this.right_goods_list = [];
        });
      }
    },
    back() {
      this.$emit("close");
    },
    really() {
      const ids = [];
      const names = [];
      this.right_goods_list.forEach((item) => {
        ids.push(item.id);
        names.push(item.name);
      });
      this.$emit(
        "update:check_id",
        this.right_goods_list.length > 0 ? ids.toString() : ""
      );
      this.$emit(
        "update:check_name",
        this.right_goods_list.length > 0 ? names.toString() : ""
      );
      this.$emit(
        "update:check_arr",
        this.right_goods_list.length > 0 ? this.right_goods_list : []
      );
      this.$emit(
        "confirm",
        this.right_goods_list.length > 0 ? this.right_goods_list : []
      );
      this.$emit("close");
    },
    currentChange(val) {
      this.page = val;
      this.getSearchGoods();
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.id;
    },
    async checkCanBuy(row) {
      const { stu_info } = this;
      const parms = {
        entity: {
          entity_id: row.id,
          entity_type: "teach_aid_package"
        },
        target: {
          department_id: stu_info.department_id,
          target_id: stu_info.student_id,
          target_status: stu_info.student_type,
          target_type: stu_info.user_type
        }
      };
      const resp = await chargeApi.charge_pre_check(parms);
      const { data } = resp.data;
      if (!data.if_can_buy) {
        this.$message.info(data.reason);
      }
      return data.if_can_buy;
    },
    async checkBoxSelect(selection, row) {
      if (this.check_can_buy) {
        const cna_buy = await this.checkCanBuy(row);
        if (!cna_buy) {
          this.right_goods_list.map((item, index) => {
            this.right_goods_list.splice(index, 1);
          });
          this.$refs.table.toggleRowSelection(row, false);
        }
      }
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag) return;
        const ids = this.right_goods_list.map((item) => item.id);
        const arr = JSON.parse(JSON.stringify(this.right_goods_list));
        const new_arr = val.map((item) => {
          if (ids.indexOf(item.id) !== -1) {
            item = arr[ids.indexOf(item.id)];
          }
          return item;
        });
        this.right_goods_list = new_arr;
      }
    },

    handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_goods_list = val == null ? [] : [val];
      }
    },
    reset() {
      this.page = 1;
      this.form = {
        name: ""
      };
      this.getSearchGoods();
    },
    searchVal() {
      this.page = 1;
      this.getSearchGoods();
    },
    rowClick(row) {
      // if (this.type === "radio" || row.is_sell === 2) return false;
      // const ids = this.right_goods_list.map((item) => item.id);
      // const index = ids.indexOf(row.id);
      // this.$refs.table.toggleRowSelection(row, index === -1);
    },
    getSearchGoods() {
      this.getGoods({
        page: this.page,
        page_size: this.page_size,
        department_id: this.department_id,
        ...this.form
      });
    },

    async getCategroyList(d) {
      const { data } = await goodsApi.getCategroyList(d);
      this.categroy_list = [{ name: "全部", id: "" }, ...data];
    },
    getGoods(data) {
      goodsApi.getTeachAidPackageSellList(data).then((res) => {
        if (this.is_charge) {
          const { data } = res;
          this.list = data.data.results || [];
          this.total = data.data.count || 0;
        }

        this.search_flag = false;
        this.$nextTick(() => {
          if (this.type === "radio") {
            this.right_goods_list.forEach((row) => {
              const find_row = this.list.find((item) => item.id === row.id);
              this.$refs.table.setCurrentRow(find_row);
            });
          } else {
            setTimeout(() => {
              this.rowSelectFlag = true;
              this.list.forEach((row) => {
                const find_index = this.right_goods_list.findIndex(
                  (item) => item.id === row.id
                );
                this.$refs.table.toggleRowSelection(row, find_index !== -1);
              });
              this.rowSelectFlag = false;
            }, 0);
          }
        });
      });
    }
  },
  created() {
    this.right_goods_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  }
};
</script>
<style lang="less" scoped>
.choose-goods-package {
  .search {
    width: 100%;
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }
    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 178px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
      ::v-deep .el-input__suffix {
        right: 1px;
        background: #ebf4ff;
        height: 30px;
        top: 1px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    .search-teacher {
      ::v-deep .el-input {
        width: 170px;
        .el-input__inner {
          padding-left: 16px;
        }
      }
    }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-form--special {
      margin-right: 10px;
    }
    // ::v-deep .tg-form-item {
    //   margin-right: 20px;
    //   .el-input {
    //     width: 296px;
    //   }
    // }
  }
  ::v-deep .el-dialog__body {
    padding: 0;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
  }
  .class-list {
    width: calc(100% - 274px);
    border-right: 1px solid #e0e6ed;
    padding: 16px;
    .tg-table__box {
      margin-left: 0;
      margin-right: 0;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0;
        background: #f5f8fc;
      }
      .el-table__body {
        padding: 0;
      }
      // .el-table__row {
      //   td {
      //     margin-right: 5px;
      //   }
      // }
    }
  }
  .class-list--right {
    width: 257px;
    margin-left: 16px;
    margin-right: 16px;
    margin-top: 16px;
    height: 414px;
    overflow: auto;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .tg-select,
  ::v-deep .el-form-item__content {
    width: 100%;
  }
  .choose-categroy {
    width: calc(100% - 468px - 88px);
    margin-right: 0;
    ::v-deep .el-input {
      width: 100%;
    }
    ::v-deep .el-select-dropdown {
      width: 240px;
    }
  }
}
</style>
