<template>
  <div>
    <v-carousel height="500" hide-delimiters show-arrows-on-hover>
      <v-carousel-item v-for="(example, i) in examples" :key="i">
        <v-sheet tile>
          <v-row align="center" justify="center">
            <v-col
              align="center"
              class="pt-4 pb-1"
              style="font-size: 20px; background: #616161"
            >
              {{ example.title }}
            </v-col>
          </v-row>
          <v-row class="fill-width" align="center" justify="center">
            <v-img :src="example.url" contain>
              <template v-slot:placeholder>
                <v-row class="fill-height" align="center" justify="center">
                  <v-progress-circular
                    indeterminate
                    color="grey lighten-5"
                  ></v-progress-circular>
                </v-row>
              </template>
            </v-img>
          </v-row>
        </v-sheet>
      </v-carousel-item>
    </v-carousel>
    <v-row>
      <v-col v-for="item in docUrls" :key="item.url">
        <v-btn
          :outlined="item.outlined"
          :color="item.color"
          dark
          @click="window.open(item.url)"
          block
        >
          <v-icon left dark>
            {{ item.icon }}
          </v-icon>
          {{ item.title }}
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: "Document",
  data() {
    return {
      window,
      examples: [
        {
          title: "学科公式",
          url: "https://demo.qcloudtiw.com/web/latest/document/学科公式.gif"
        },
        {
          title: "数学函数",
          url: "https://demo.qcloudtiw.com/web/latest/document/数学函数.gif"
        },
        {
          title: "魔法笔",
          url: "https://demo.qcloudtiw.com/web/latest/document/魔法笔.gif"
        }
      ],
      docUrls: [
        {
          icon: "mdi-cloud-download",
          color: "primary",
          title: "demo下载",
          url: "https://demo.qcloudtiw.com/web/latest/web-demo.zip"
        },
        {
          icon: "mdi-file-document",
          color: "blue lighten-1",
          title: "SDK文档",
          url: "https://doc.qcloudtiw.com/web/index.html"
        },
        {
          icon: "mdi-cloud",
          color: "grey darken-3",
          title: "官网地址",
          url: "https://cloud.tencent.com/document/product/1137"
        }
      ]
    };
  }
};
</script>
