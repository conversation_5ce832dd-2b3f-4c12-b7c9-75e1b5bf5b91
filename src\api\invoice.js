/**
 * 发票管理接口
 */

import qs from "qs";
const fetch = require("../fetch");
const api_path = "/api/finance-service/invoice";
// 发票列表
function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${api_path}/list?${new_data}`);
}

// 开票
function agree(data) {
  return fetch.fetchPost(`${api_path}/agree`, data, "");
}
// 申请开票
function apply(data) {
  return fetch.fetchPost(`${api_path}/apply`, data, "");
}

// 拒绝开票
function refuse(data) {
  return fetch.fetchPost(`${api_path}/refuse`, data, "");
}
// 作废发票
function discard(data) {
  return fetch.fetchPost(`${api_path}/discard`, data, "");
}
// 发票详情
function getInfo(data) {
  return fetch.fetchGet(`${api_path}/info`, {
    params: data
  });
}
// 收据开票状态
function getInvoiceStatus(data) {
  return fetch.fetchGet(`/api/finance-service/receipt/hasInvoice`, {
    params: data
  });
}
export default {
  getList,
  agree,
  refuse,
  getInfo,
  apply,
  getInvoiceStatus,
  discard
};
