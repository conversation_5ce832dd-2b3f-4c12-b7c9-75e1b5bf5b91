<template>
  <div class="barchatBox">
    <div
      ref="chart"
      style="width: 90%; height: 95%; margin-left: 5%"
      v-show="echartsData?.timeList?.length > 0"
    ></div>
    <div class="noData" v-show="echartsData?.timeList?.length === 0">
      暂无数据
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: ["echartsData"],
  data() {
    return {
      echartsCtx: null,
      timered: null
    };
  },
  watch: {
    // echartsData: {
    //   handler() {
    //     console.log("this.echartsDat2222a", this.echartsData);
    //     this.setOption(this.echartsData);
    //   },
    //   deep: true
    //   // immediate: true
    // }
  },
  mounted() {
    this.initEchart();
    // this.setOption(this.echartsData);
  },
  methods: {
    // 初始化echarts
    initEchart() {
      this.echartsCtx = echarts.init(this.$refs.chart);
      this.timered = setInterval(() => {
        console.log("定时器");
        if (this.echartsData) {
          this.setOption(this.echartsData);
        }
      }, 3000);
    },
    setOption(data) {
      // console.log("setOption", data);
      if (!data) {
        data = {
          timeList: []
        };
      }
      const option = {
        animation: false,
        // 开启渐进式渲染
        progressive: 10,
        // 渲染阈值，大于此值则启动渐进渲染
        progressiveThreshold: 10,
        xAxis: [
          {
            type: "category",
            // boundaryGap: false,
            data: data.timeList,
            axisLabel: {
              color: "#438EFF",
              fontSize: 16,
              fontWeight: "bold"
            }
          }
        ],
        yAxis: [
          {
            type: "value",
            // minInterval: 1,
            splitLine: {
              show: true,
              lineStyle: {
                type: "solid",
                color: "#D8D8D8",
                width: 2
              }
            },
            axisPointer: {
              type: "none"
            },
            axisLabel: {
              color: "#438EFF",
              fontSize: 16,
              fontWeight: "bold"
            }
          }
        ],
        grid: {
          left: 40,
          bottom: 30,
          top: 30
        },
        series: [
          {
            // 开启渐进式渲染
            progressive: 10,
            // 渲染阈值，大于此值则启动渐进渲染
            progressiveThreshold: 10,
            data: data.dataList,
            type: "bar",
            sampling: "lttb",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)"
            },
            itemStyle: {
              // 设置柱状图的渐变色
              color: new echarts.graphic.LinearGradient(
                0, // 渐变起始点 x 坐标
                0, // 渐变起始点 y 坐标
                0, // 渐变结束点 x 坐标
                1, // 渐变结束点 y 坐标
                [
                  { offset: 0, color: "#07C7F1" }, // 渐变颜色的起始位置，偏移值为 0
                  { offset: 1, color: "#3272EE" } // 渐变颜色的结束位置，偏移值为 1
                ]
              )
            },
            barMaxWidth: 40
          }
        ]
      };
      this.echartsCtx.setOption(option); //  挂载数据
      this.$nextTick(() => {
        this.echartsCtx.resize();
        window.addEventListener("resize", () => {
          this.echartsCtx.resize();
        });
      });
    }
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {
      this.echartsCtx.resize();
      this.echartsCtx.clear();
      this.echartsCtx.dispose();
    });
    clearInterval(this.timered);
  }
};
</script>
<style scoped lang="less">
.barchatBox {
  width: 100%;
  height: 100%;
  display: inline-block;
  // margin-top: 24px;
  // background-image: url("../../../../assets/cockpit/barBg.png");
  // background-repeat: no-repeat;
  // background-size: 100% 100%;
  .noData {
    display: inline-flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
  }
}
</style>
