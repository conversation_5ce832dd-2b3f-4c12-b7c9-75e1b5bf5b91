<!--选择仓库中的物品-->
<template>
  <el-dialog
    :visible="true"
    title="选择物品"
    width="1216px"
    :before-close="handleClose"
    class="choose-warehouse-goods"
    :modal="has_modal"
    :append-to-body="true"
  >
    <div class="tg-dialog__content">
      <div class="class-list">
        <div class="search tg-box--margin">
          <el-form :inline="true" :model="form">
            <el-form-item>
              <el-input
                placeholder="请输入物品名称"
                class="search__input"
                v-model="form.name"
                @keyup.enter.native="searchVal()"
              >
                <img
                  src="../../assets/图片/icon_search_grey.png"
                  alt=""
                  slot="prefix"
                />
                <!-- <img
                  :src="
                    !flag
                      ? require('../../assets/图片/icon_double_down.png')
                      : require('../../assets/图片/icon_double_up_ac.png')
                  "
                  alt=""
                  slot="suffix"
                  class="search__img"
                  @click="flag = !flag"
                /> -->
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                style="margin-right: 10px"
                placeholder="请输入物品代码"
                class="search__input"
                v-model="form.article_code"
                @keyup.enter.native="searchVal()"
              >
                <img
                  src="../../assets/图片/icon_search_grey.png"
                  alt=""
                  slot="prefix"
                />
                <!-- <img
                  :src="
                    !flag
                      ? require('../../assets/图片/icon_double_down.png')
                      : require('../../assets/图片/icon_double_up_ac.png')
                  "
                  alt=""
                  slot="suffix"
                  class="search__img"
                  @click="flag = !flag"
                /> -->
              </el-input>
            </el-form-item>
            <el-form-item>
              <!-- 请选择销售方式 -->
              <el-select
                v-model="form.sales_method"
                placeholder="请选择销售方式"
                style="width: 178px"
                :disabled="is_lease"
              >
                <el-option label="销售" :value="1"></el-option>
                <el-option label="租赁" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="tg-form--special">
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="searchVal"
              >
                <img
                  src="../../assets/图片/icon_search.png"
                  alt=""
                  class="tg-button__icon--normal"
                />查询
              </el-button>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="reset"
              >
                <img
                  src="../../assets/图片/icon_reset.png"
                  alt=""
                  class="tg-button__icon--normal"
                />重置
              </el-button>
            </el-form-item>
            <el-form-item class="choose-categroy" v-if="!goodsType">
              <el-select
                placeholder="请选择物品类别"
                :popper-append-to-body="false"
                v-model="form.article_category_id"
                class="tg-select"
              >
                <el-option
                  v-for="(item, index) in categroy_list"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="list"
            tooltip-effect="dark"
            class="tg-table"
            :height="flag ? 353 : 449"
            @selection-change="handleSelectionChange"
            @current-change="handleCurrentChange"
            :row-key="getRowKeys"
            @select="handleSelect"
            :highlight-current-row="type == 'radio' ? true : false"
            @row-click="rowClick"
            @sort-change="sortChange"
          >
            <el-table-column
              type="selection"
              width="50"
              :selectable="checkSelectable"
              :reserve-selection="true"
              v-if="type != 'radio'"
            ></el-table-column>
            <el-table-column
              width="20"
              v-if="type == 'radio'"
            ></el-table-column>
            <el-table-column
              label="物品名称"
              prop="name"
              width="300"
              sortable="custom"
            ></el-table-column>
            <el-table-column
              label="物品类别"
              prop="article_category_name"
              width="100"
            ></el-table-column>
            <el-table-column
              label="物品代码"
              prop="article_code"
              width="86"
            ></el-table-column>
            <el-table-column
              label="物品规格"
              prop="article_norms"
              width="86"
            ></el-table-column>
            <el-table-column label="销售单价" prop="sell_price" width="100">
            </el-table-column>
            <el-table-column
              label="租赁周期"
              prop="sales_cycle"
              width="100"
            ></el-table-column>
            <el-table-column
              label="单位"
              prop="unit"
              :width="type == 'radio' ? 110 : 80"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="销售方式" prop="sales_method" width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.sales_method === 1">销售</span>
                <span v-else>租赁</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="is_charge"
              label="剩余库存"
              prop="current_number"
              width="80"
            ></el-table-column>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="total"
              :page-size="page_size"
              :current-page="page"
              @current-change="currentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="class-list--right">
        <div class="organization__title">
          <span
            >已选 物品<em>{{ right_goods_list.length }}</em
            >个</span
          >
          <span class="all-clear" @click="clear">
            <img src="../../assets/图片/icon_clear.png" alt="" />
            清空
          </span>
        </div>
        <div
          class="organization__info"
          v-for="(item, index) in right_goods_list"
          :key="index"
        >
          <span>{{ item.name }}</span>
          <img
            src="../../assets/图片/icon_close_green.png"
            alt=""
            @click="delOne(index, item.id)"
          />
        </div>
        <span v-if="right_goods_list.length === 0" class="is-empty"
          >暂无数据</span
        >
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >取消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import goodsApi from "@/api/goods";
import wmsManagementApi from "@/api/wmsManagement";

export default {
  data() {
    return {
      right_goods_list: [],
      flag: false,
      sort: "",
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      form: {
        name: "",
        article_category_id: "",
        is_enabled: "",
        article_code: "",
        sales_method: ""
      },
      search_flag: false,
      rowSelectFlag: false,
      categroy_list: [],
      preventSelectionChange: false
    };
  },
  props: {
    check_id: String,
    check_name: String,
    check_arr: {
      type: Array,
      default: () => []
    },
    has_modal: {
      type: Boolean,
      default: true
    },
    department_id: Array,
    type: {
      type: String,
      default: "select"
    },
    status: {
      type: Boolean,
      default: false // true只显示启用的列表
    },
    exclude_id: {
      type: Array,
      default: () => [] // exclude
    },
    // true 为调用仓库下的库存，false为全部库存
    goodsType: {
      type: Boolean,
      default: false
    },
    // 仓库Id
    kuId: {
      type: String,
      default: ""
    },
    // 是否收费页面的选择物品
    is_charge: {
      type: Boolean,
      default: false
    },
    // 租赁 售卖的物品是否只允许选中一种
    is_lease_package: {
      type: Boolean,
      default: false
    },
    // 当前页面已选中的其他商品
    listGoods: {
      type: Array,
      default: () => []
    },
    // 是否只选择租赁商品
    is_lease: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    if (this.is_lease) {
      this.form.sales_method = 2;
    }
    this.form.is_enabled = this.status ? 1 : "";
    this.right_goods_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
    console.log(!this.goodsType);
    if (!this.goodsType) {
      this.getCategroyList();
      this.getGoods({
        page: this.page,
        page_size: this.page_size,
        department_id: this.department_id,
        ...this.form,
        no_course_id: this.exclude_id
      });
    } else {
      const query = {
        page: this.page,
        page_size: this.page_size,
        article_name: this.form.name,
        article_bank_id: this.kuId
      };
      this.getWarehouseGoods(query);
    }
  },
  methods: {
    checkSelectable(row) {
      return row.current_number !== 0;
    },
    handleClose() {
      this.$emit("close");
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_goods_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.$nextTick(() => {
            this.rowSelectFlag = true;
            const id = this.right_goods_list[index].id;
            this.list.forEach((item) => {
              if (item.id === id) {
                this.$refs.table.toggleRowSelection(item, false);
              }
            });
            this.right_goods_list.splice(index, 1);
            this.rowSelectFlag = false;
          });
        }, 0);
      }
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.table.clearSelection();
          this.right_goods_list = [];
        });
      }
    },
    back() {
      this.$emit("close");
    },
    really() {
      const ids = [];
      const names = [];
      this.right_goods_list.forEach((item) => {
        ids.push(item.id);
        names.push(item.name);
      });
      this.$emit(
        "update:check_id",
        this.right_goods_list.length > 0 ? ids.toString() : ""
      );
      this.$emit(
        "update:check_name",
        this.right_goods_list.length > 0 ? names.toString() : ""
      );
      this.$emit(
        "update:check_arr",
        this.right_goods_list.length > 0 ? this.right_goods_list : []
      );
      this.$emit("confirm");
      this.$emit("close");
    },
    currentChange(val) {
      this.page = val;
      if (!this.goodsType) {
        this.getSearchGoods();
      } else {
        this.getSearchWarehouseGoods();
      }
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.id;
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag || this.preventSelectionChange) {
          this.preventSelectionChange = false;
          return;
        }
        const ids = this.right_goods_list.map((item) => item.id);
        const arr = JSON.parse(JSON.stringify(this.right_goods_list));
        const new_arr = val.map((item) => {
          if (ids.indexOf(item.id) !== -1) {
            item = arr[ids.indexOf(item.id)];
          }
          return item;
        });
        this.right_goods_list = new_arr;
      }
    },
    handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_goods_list = val == null ? [] : [val];
      }
    },
    reset() {
      this.page = 1;
      this.form = {
        name: "",
        article_category_id: "",
        is_enabled: this.status ? 1 : "",
        sales_method: ""
      };
      if (!this.goodsType) {
        this.getSearchGoods();
      } else {
        this.getSearchWarehouseGoods();
      }
      // this.$refs.table.clearSelection();
    },
    searchVal() {
      this.page = 1;
      if (!this.goodsType) {
        this.getSearchGoods();
      } else {
        this.getSearchWarehouseGoods();
      }
    },
    handleSelect(selection, row) {
      this.rowClick(row);
    },
    rowClick(row) {
      if (this.type === "radio" || row.current_number === 0) return false;
      const isLeaseItem = this.is_lease_package && row.sales_method === 2;
      if (this.listGoods.length > 0 && isLeaseItem) {
        this.$message.warning("租赁物品不能与其他商品混用");
        this.$refs.table.toggleRowSelection(row, false);
        this.preventSelectionChange = true;
        return false;
      }
      if (
        this.is_lease_package &&
        this.right_goods_list.length > 0 &&
        this.right_goods_list[0].sales_method !== row.sales_method
      ) {
        this.$message.warning("物品只能选择同一销售方式，租赁物品只能选择一个");
        this.$refs.table.toggleRowSelection(row, false);
        this.preventSelectionChange = true;
        return false;
      }
      if (isLeaseItem) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(row, true);
        this.preventSelectionChange = true;
        this.right_goods_list = [row];
        return;
      }
      const ids = this.right_goods_list.map((item) => item.id);
      const index = ids.indexOf(row.id);
      this.$refs.table.toggleRowSelection(row, index === -1);
    },
    sortChange(val) {
      let { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      if (prop.startsWith("student_base.")) {
        prop = prop.replace("student_base.", "");
      }
      // this.sort = `${prop}_${_oreder}`;
      this.sort = `${_oreder}`;
      this.searchVal();
    },
    getSearchGoods() {
      this.getGoods({
        page: this.page,
        page_size: this.page_size,
        name_desc: this.sort,
        department_id: this.department_id,
        ...this.form,
        no_course_id: this.exclude_id
      });
    },
    getSearchWarehouseGoods() {
      const query = {
        page: this.page,
        page_size: this.page_size,
        article_bank_id: this.kuId,
        name_desc: this.sort,
        article_name: this.form.name,
        article_code: this.form.article_code
      };
      this.getWarehouseGoods(query);
    },
    async getCategroyList(d) {
      const { data } = await goodsApi.getCategroyList(d);
      this.categroy_list = [{ name: "全部", id: "" }, ...data];
    },
    getGoods(data) {
      let list_api_method = "getGoodsList";
      if (this.is_charge) {
        list_api_method = "getWarehousAmountList";
      }
      goodsApi[list_api_method](data).then((res) => {
        if (this.is_charge) {
          const { data } = res;
          this.list = data.data.results || [];
          this.total = data.data.count || 0;
        } else {
          this.list = res.data.results == null ? [] : res.data.results;
          this.total = res.data.count;
        }

        this.search_flag = false;
        this.$nextTick(() => {
          if (this.type === "radio") {
            this.right_goods_list.forEach((row) => {
              const find_row = this.list.find((item) => item.id === row.id);
              this.$refs.table.setCurrentRow(find_row);
            });
          } else {
            setTimeout(() => {
              this.rowSelectFlag = true;
              this.list.forEach((row) => {
                const find_index = this.right_goods_list.findIndex(
                  (item) => item.id === row.id
                );
                this.$refs.table.toggleRowSelection(row, find_index !== -1);
              });
              this.rowSelectFlag = false;
            }, 0);
          }
        });
      });
    },
    getWarehouseGoods(data) {
      wmsManagementApi.getWarehouseGoods(data).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          const data = res.data.data;
          this.total = data.count;
          this.list = data.results
            ? data.results.map((item) => {
                return {
                  name: item.article_name,
                  article_category_name: item.article_category_name,
                  article_code: "",
                  article_norms: "",
                  sell_price: item.sell_price,
                  unit: item.unit,
                  id: item.article_id,
                  ...item
                };
              })
            : [];
          this.$nextTick(() => {
            if (this.type === "radio") {
              this.right_goods_list.forEach((row) => {
                const find_row = this.list.find((item) => item.id === row.id);
                this.$refs.table.setCurrentRow(find_row);
              });
            } else {
              setTimeout(() => {
                this.rowSelectFlag = true;
                this.list.forEach((row) => {
                  const find_index = this.right_goods_list.findIndex(
                    (item) => item.id === row.id
                  );
                  this.$refs.table.toggleRowSelection(row, find_index !== -1);
                });
                this.rowSelectFlag = false;
              }, 0);
            }
          });
        }
      });
    }
  },
  created() {
    this.right_goods_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  }
};
</script>
<style lang="less" scoped>
.choose-warehouse-goods {
  .search {
    width: 100%;
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }
    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 178px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
      ::v-deep .el-input__suffix {
        right: 1px;
        background: #ebf4ff;
        height: 30px;
        top: 1px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    .search-teacher {
      ::v-deep .el-input {
        width: 170px;
        .el-input__inner {
          padding-left: 16px;
        }
      }
    }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-form--special {
      margin-right: 10px;
    }
    // ::v-deep .tg-form-item {
    //   margin-right: 20px;
    //   .el-input {
    //     width: 296px;
    //   }
    // }
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
  }
  .class-list {
    width: calc(100% - 274px);
    border-right: 1px solid #e0e6ed;
    .tg-table__box {
      margin-left: 0;
      margin-right: 16px;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0 16px;
        background: #f5f8fc;
      }
      .el-table__body {
        padding: 0 16px;
      }
      // .el-table__row {
      //   td {
      //     margin-right: 5px;
      //   }
      // }
    }
  }
  .class-list--right {
    width: 257px;
    margin-left: 16px;
    margin-top: 16px;
    height: 414px;
    overflow: auto;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .tg-select,
  ::v-deep .el-form-item__content {
    width: 100%;
  }
  .choose-categroy {
    width: calc(100% - 468px - 88px - 188px);
    margin-right: 0;
    ::v-deep .el-input {
      width: 100%;
    }
    ::v-deep .el-select-dropdown {
      width: 240px;
    }
  }
}
</style>
