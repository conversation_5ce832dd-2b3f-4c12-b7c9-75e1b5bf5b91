<!--选择班级-->
<template>
  <div>
    <el-dialog
      :visible="true"
      width="1016px"
      :before-close="back"
      class="inventory-detail-dialog"
    >
      <div slot="title" class="dialog-title">
        <span>转化详情-</span>
        <span class="special">{{
          requestData.receive_status === "4" ? "未到店" : "已到店"
        }}</span>
      </div>
      <el-row
        class="tg-shadow--margin tg-row--height"
        v-has="{ m: 'front', o: 'transfer_to_student_export' }"
      >
        <el-button
          type="primary"
          @click="exportTable"
          :loading="exportLoading"
          class="tg-button--primary tg-button__icon"
        >
          <img
            src="@/assets/图片/icon_export.png"
            alt
            class="tg-button__icon--large"
          />导出
        </el-button>
      </el-row>
      <div class="tg-dialog__content tg-box--margin">
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="table_list"
            tooltip-effect="dark"
            class="tg-table"
            row-key="id"
            highlight-current-row
            style="width: 100%"
            :header-cell-style="{ 'background-color': '#F5F8FC' }"
          >
            <template v-for="(item, index) in table_title">
              <el-table-column
                v-if="item.show"
                :key="index"
                :prop="item.props"
                :label="item.label"
                :width="item.width"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <span v-if="item.type === 'date'">{{
                    scope.row[scope.column.property] | getDate
                  }}</span>
                  <div v-else-if="item.props === 'student_mobile'">
                    <mobileHyposensitization
                      :mobileTemInfo="{
                        row: scope.row,
                        has_eye_limit: scope.row?.has_eye_limit,
                        mobile: scope.row.student_mobile
                      }"
                    ></mobileHyposensitization>
                  </div>
                  <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="total"
              :page-size="page_size"
              :current-page="page"
              @current-change="currentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>

      <!-- <span slot="footer" class="dialog-footer">
          <div>
            <el-button class="tg-button--plain" type="plain" @click="back"
              >关闭</el-button
            >
          </div>
        </span> -->
    </el-dialog>
  </div>
</template>
<script>
import frontApi from "@/api/front";
import moment from "moment";
import { downLoadFile } from "@/public/downLoadFile";

export default {
  name: "DetailDialog",
  components: {},
  data() {
    return {
      // 发票相关
      // info: {},
      total: 0,
      page_size: 10,
      page: 1,
      table_title: [
        {
          props: "department_name",
          label: "所属校区",
          show: true
        },
        {
          props: "student_number",
          label: "学号",
          show: true
        },
        {
          props: "student_name",
          label: "学员姓名",
          show: true
        },
        {
          props: "student_mobile",
          label: "手机号",
          width: 160,
          show: true
        },
        {
          props: "channel_name",
          label: "渠道名称",
          show: false
        },
        {
          props: "sub_channel_name",
          label: "渠道名称",
          show: false
        },
        {
          props: "to_student_at",
          label: "转化日期",
          show: true,
          type: "date"
        },
        {
          props: "to_student_source",
          label: "转化类型",
          show: true
        }
      ],
      table_list: [],
      toutalNum: 0,
      exportLoading: false
    };
  },
  props: {
    info: {
      type: Object
    },
    requestData: {
      type: Object
    }
  },
  filters: {
    getDate(val) {
      return val ? moment(val).format("YYYY-MM-DD") : "";
    }
  },
  mounted() {
    // 详情页面
    if (this.info.search_level === 1) {
      this.table_title[4].show = true;
    } else {
      this.table_title[5].show = true;
    }
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId.toString();
    }
  },
  watch: {
    schoolIds: {
      handler(val) {
        this.init();
      },
      immediate: true
    }
  },
  methods: {
    init() {
      frontApi.getCustomerTransferRateStudent(this.getSearch()).then((res) => {
        if (+res.status === 200 && res.data) {
          this.table_list = res.data.data.results;
          this.total = res.data.data.count;
        }
      });
    },
    getSearch() {
      const { page, page_size } = this;
      return {
        page,
        page_size,
        ...this.requestData
      };
    },
    exportTable() {
      if (!this.table_list) {
        this.$message.warning("暂无可导出数据!");
      }
      frontApi
        .getCustomerTransferRateStudentExport(this.getSearch())
        .then((res) => {
          downLoadFile(res, `转化详情`);
          this.exportLoading = false;
        });
    },
    currentChange(val) {
      this.page = val;
      this.init();
    },
    back() {
      this.$emit("close");
    }
  }
};
</script>
<style lang="less" scoped>
.inventory-detail-dialog {
  .dialog-title {
    .special {
      color: @base-color;
    }
  }

  .info {
    border: 1px solid @base-color;
    border-radius: 4px;
    overflow: hidden;
  }

  .info-box__border {
    box-sizing: border-box;
    border-bottom: 1px solid #e0e6ed;
    border-right: 1px solid #e0e6ed;

    &:nth-child(4n) {
      border-right: 0;
    }

    &:last-child {
      border-right: 0;
    }
  }

  .tg-table__box {
    margin: 0;
    // margin-top: 16px;
    height: 100%;
    box-shadow: none;
    background: #f5f8fc;
  }
  .tg-button__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    ::v-deep > span {
      display: flex;
    }
    .tg-button__icon--large {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: auto;
    .el-table {
      padding: 0;
    }
  }

  .tg-dialog__content {
    height: 100%;
  }
}
</style>
